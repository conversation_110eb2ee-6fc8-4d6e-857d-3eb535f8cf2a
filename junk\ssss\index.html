<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>AutoSpace - Document Processing Platform</title>
  <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}" />
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;600;700&display=swap" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
</head>
<body>
  <div class="container">
    <!-- Navigation Header -->
    <header class="nav-header">
      <a href="/home" class="nav-logo"><img src="static/logo.png" alt=""></a>
      <ul class="nav-links">
        <li><a href="/home" id="home-link">Dashboard</a></li>
        <li><a href="#" id="autospace-link">AutoSpace</a></li>
        <li><a href="/about" id="about-link">About</a></li>
      </ul>
      <div class="nav-actions">
        <span class="user-info" id="user-info">Loading...</span>
        <button class="btn btn-secondary btn-sm" id="logout-btn">
          <i class="fas fa-sign-out-alt"></i> Logout
        </button>
      </div>
    </header>

    <!-- Main Content Area -->
    <div class="content main-content">
      <!-- Side Bar -->
      <aside class="side-bar">
        <div class="tabs">
          <div class="tab" id="extraction" data-category="CAT001">
            <i class="fas fa-file-text"></i>
            <span>EXTRACTION</span>
          </div>
          <div class="tab" id="comparison" data-category="CAT002">
            <i class="fas fa-search"></i>
            <span>COMPARISON</span>
          </div>
          <div class="tab" id="search" data-category="CAT003">
            <i class="fas fa-search-plus"></i>
            <span>SEARCH</span>
          </div>
          <div class="tab" id="analysis" data-category="CAT004">
            <i class="fas fa-chart-bar"></i>
            <span>ANALYSIS</span>
          </div>
          <div class="tab admin-tab" id="admin" data-category="ADMIN" style="display: none;">
            <i class="fas fa-users-cog"></i>
            <span>ADMIN</span>
          </div>
        </div>
      </aside>

      <!-- Tool Cards Section -->
      <section class="tools-section section-container"></section>

      <!-- Middle Section -->
      <section class="middle-section section-container">
        <div class="description-section section">
          <h3 class="box-title">
            <i class="fas fa-cog"></i> Tool Configuration
          </h3>
          <div class="description-box box">
            <div class="tool-header fixed-header">
              <h4 class="tool-name">Select a tool to get started</h4>
              <span class="tool-id"></span>
            </div>

            <div class="tool-description scrollable-content">
              Choose a tool from the categories on the left to see its description and configuration options.
            </div>

            <div class="button-section fixed-footer">
              <div class="file-info" id="file-info" style="display:none;">
                <i class="fas fa-file"></i>
                <span id="file-name">No file selected</span>
                <span id="file-size"></span>
              </div>

              <div class="button-container">
                <label for="file-input" class="btn btn-secondary upload-button d-buttons" id="upload">
                  <i class="fas fa-upload"></i> Upload File
                </label>
                <input type="file" id="file-input" style="display:none;" />

                <button class="btn btn-primary run-button d-buttons" id="run">
                  <i class="fas fa-play"></i> Run Tool
                </button>
                <button class="btn btn-secondary download-sample-button d-buttons" id="download">
                  <i class="fas fa-download"></i> Download Sample
                </button>

                <div class="mode-selection d-buttons" style="display:none;">
                  <label class="mode-label">
                    <input type="checkbox" class="mode-button" id="run-mode" />
                    <span class="mode-text">Fast Mode</span>
                    <i class="fas fa-bolt"></i>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
 
        <div class="progress-section section">
          <h3 class="box-title">
            <i class="fas fa-tasks"></i> Session Management
          </h3>
          <div class="smaller-container">
            <div class="progress-box box">
              <h4 class="box-title fixed-header">
                <i class="fas fa-spinner fa-pulse"></i> Active Sessions
                <span class="session-count" id="active-count">0</span>
                <span class="session-limit">(Max 3)</span>
              </h4>
              <div class="sessions-container scrollable-content" id="active-sessions">
                <div class="no-sessions">
                  <i class="fas fa-info-circle"></i>
                  <p>No active sessions</p>
                </div>
              </div>
            </div>
            <div class="finished-box box">
              <h4 class="box-title fixed-header">
                <i class="fas fa-check-circle"></i> Completed Sessions
                <span class="session-count" id="finished-count">0</span>
                <span class="session-limit">(Last 10)</span>
              </h4>
              <div class="sessions-container scrollable-content" id="finished-sessions">
                <div class="no-sessions">
                  <i class="fas fa-info-circle"></i>
                  <p>No completed sessions</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Right Section -->
      <section class="right-section section-container">
        <div class="visualization-section section">
          <h3 class="box-title">
            <i class="fas fa-chart-line"></i> System Performance
          </h3>
          <div class="visualization-box box">
            <div class="performance-metrics">
              <div class="metric">
                <i class="fas fa-memory"></i>
                <span class="metric-label">Memory</span>
                <span class="metric-value" id="memory-info">Loading...</span>
              </div>
              <div class="metric">
                <i class="fas fa-microchip"></i>
                <span class="metric-label">CPU Cores</span>
                <span class="metric-value" id="cpu-info">Loading...</span>
              </div>
              <div class="metric">
                <i class="fas fa-desktop"></i>
                <span class="metric-label">Platform</span>
                <span class="metric-value" id="platform-info">Loading...</span>
              </div>
              <div class="metric">
                <i class="fas fa-clock"></i>
                <span class="metric-label">Uptime</span>
                <span class="metric-value" id="uptime-info">Loading...</span>
              </div>
            </div>
          </div>
        </div>
        <div class="users-section section">
          <h3 class="box-title fixed-header">
            <i class="fas fa-users"></i> Active Users
            <span class="session-count" id="active-users-count">0</span>
          </h3>
          <div class="users-box box">
            <div class="users-container scrollable-content" id="active-users-list">
              <div class="no-users">
                <i class="fas fa-info-circle"></i>
                <p>Loading users...</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Admin Section -->
      <section class="admin-section section-container" id="admin-section" style="display: none;">
        <div class="admin-overview">
          <h2 class="section-title">
            <i class="fas fa-users-cog"></i> Admin Dashboard
          </h2>

          <!-- System Statistics -->
          <div class="admin-stats-grid">
            <div class="stat-card">
              <div class="stat-icon">
                <i class="fas fa-users"></i>
              </div>
              <div class="stat-content">
                <h3 id="total-users-count">0</h3>
                <p>Total Users</p>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">
                <i class="fas fa-tasks"></i>
              </div>
              <div class="stat-content">
                <h3 id="total-sessions-count">0</h3>
                <p>Total Sessions</p>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">
                <i class="fas fa-check-circle"></i>
              </div>
              <div class="stat-content">
                <h3 id="success-rate">0%</h3>
                <p>Success Rate</p>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">
                <i class="fas fa-clock"></i>
              </div>
              <div class="stat-content">
                <h3 id="recent-sessions">0</h3>
                <p>Last 24h</p>
              </div>
            </div>
          </div>

          <!-- Users Management -->
          <div class="admin-users-section">
            <h3 class="box-title">
              <i class="fas fa-users"></i> User Management
            </h3>
            <div class="admin-users-container">
              <div class="users-table-container">
                <table class="users-table">
                  <thead>
                    <tr>
                      <th>User</th>
                      <th>Status</th>
                      <th>Sessions</th>
                      <th>Success Rate</th>
                      <th>Last Activity</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody id="admin-users-table">
                    <tr>
                      <td colspan="6" class="loading-row">Loading users...</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- All Sessions Overview -->
          <div class="admin-sessions-section">
            <h3 class="box-title">
              <i class="fas fa-list"></i> All Sessions Overview
            </h3>
            <div class="admin-sessions-container">
              <div class="sessions-filter">
                <select id="session-status-filter">
                  <option value="all">All Sessions</option>
                  <option value="running">Running</option>
                  <option value="pending">Pending</option>
                  <option value="done">Completed</option>
                  <option value="error">Failed</option>
                  <option value="killed">Killed</option>
                </select>
                <select id="session-user-filter">
                  <option value="all">All Users</option>
                </select>
              </div>
              <div class="sessions-table-container">
                <table class="sessions-table">
                  <thead>
                    <tr>
                      <th>Session ID</th>
                      <th>User</th>
                      <th>Tool</th>
                      <th>Status</th>
                      <th>Progress</th>
                      <th>Created</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody id="admin-sessions-table">
                    <tr>
                      <td colspan="7" class="loading-row">Loading sessions...</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>

    <!-- Footer -->
    <footer>
      <div class="copyright secondary-text">
        © <EMAIL> All rights reserved.
      </div>
    </footer>
  </div>

  <script type="module">
    import { toolsData, toolCategories } from '{{ url_for("static", filename="data.js") }}';

    // Global variables
    let uploadedFile = null;
    let selectedTool = null;
    let currentSessionId = null;
    let sessionUpdateInterval = null;

    // Elements
    const navTabs = document.querySelectorAll('.tab');
    const toolsSection = document.querySelector('.tools-section');
    const descriptionBox = document.querySelector('.description-box');
    const runButton = descriptionBox.querySelector('.run-button');
    const uploadButton = descriptionBox.querySelector('.upload-button');
    const downloadSampleButton = descriptionBox.querySelector('.download-sample-button');
    const dButtons = document.querySelectorAll('.d-buttons');
    const fileInput = document.getElementById('file-input');
    const modeButton = document.querySelector('.mode-button');
    const activeSessionsContainer = document.getElementById('active-sessions');
    const finishedSessionsContainer = document.getElementById('finished-sessions');
    const logoutBtn = document.getElementById('logout-btn');
    const userInfo = document.getElementById('user-info');
    const fileInfo = document.getElementById('file-info');
    const fileName = document.getElementById('file-name');
    const fileSize = document.getElementById('file-size');

    // Initialize application
    async function initializeApp() {
      try {
        // Check authentication status and get user info
        const response = await fetch('/api/sessions');
        if (response.status === 401) {
          window.location.href = '/login';
          return;
        }

        // Get current user info
        try {
          const userResponse = await fetch('/api/user');
          if (userResponse.ok) {
            const userData = await userResponse.json();
            userInfo.textContent = `Welcome, ${userData.username}`;
          } else {
            userInfo.textContent = 'Welcome, User';
          }
        } catch (e) {
          userInfo.textContent = 'Welcome, User';
        }

        // Load initial data
        await loadSessions();
        await loadActiveUsers();
        showPerformance();
        startSessionPolling();
        startUserPolling();

      } catch (error) {
        console.error('Failed to initialize app:', error);
        window.location.href = '/login';
      }
    }

    // File input handler
    fileInput.addEventListener('change', () => {
      const file = fileInput.files[0];
      if (file) {
        uploadedFile = file;
        fileName.textContent = file.name;
        fileSize.textContent = `(${(file.size / 1024).toFixed(1)} KB)`;
        fileInfo.style.display = 'flex';
      } else {
        fileInfo.style.display = 'none';
      }
    });

    // Logout handler
    logoutBtn.addEventListener('click', async () => {
      try {
        await fetch('/logout');
        window.location.href = '/login';
      } catch (error) {
        console.error('Logout failed:', error);
      }
    });

    // Performance monitoring
    function showPerformance() {
      document.getElementById('memory-info').textContent =
        navigator.deviceMemory ? `${navigator.deviceMemory} GB` : 'Unknown';
      document.getElementById('cpu-info').textContent =
        navigator.hardwareConcurrency || 'Unknown';
      document.getElementById('platform-info').textContent =
        navigator.platform || 'Unknown';

      // Update uptime
      const startTime = Date.now();
      setInterval(() => {
        const uptime = Math.floor((Date.now() - startTime) / 1000);
        const minutes = Math.floor(uptime / 60);
        const seconds = uptime % 60;
        document.getElementById('uptime-info').textContent =
          `${minutes}m ${seconds}s`;
      }, 1000);
    }

    // Session management functions
    async function loadSessions() {
      try {
        const response = await fetch('/api/sessions');
        if (response.ok) {
          const sessions = await response.json();
          displaySessions(sessions);
          updateSessionStats(sessions);
          return sessions;
        } else if (response.status === 401) {
          // Authentication expired
          window.location.href = '/login';
          return null;
        } else {
          console.error('Failed to load sessions:', response.statusText);
          showNotification('Failed to load sessions', 'error');
          return null;
        }
      } catch (error) {
        console.error('Failed to load sessions:', error);
        showNotification('Network error loading sessions', 'error');
        return null;
      }
    }

    function displaySessions(sessions) {
      const activeSessions = [];
      const finishedSessions = [];

      Object.entries(sessions).forEach(([sessionId, sessionData]) => {
        if (sessionData.status === 'done' || sessionData.status === 'error' || sessionData.status === 'killed') {
          finishedSessions.push({ id: sessionId, ...sessionData });
        } else {
          activeSessions.push({ id: sessionId, ...sessionData });
        }
      });

      // Sort finished sessions by timestamp (newest first) and limit to last 10
      finishedSessions.sort((a, b) => new Date(b.updated_at) - new Date(a.updated_at));
      const limitedFinishedSessions = finishedSessions.slice(0, 10);

      // Display active sessions (max 3 running)
      if (activeSessions.length === 0) {
        activeSessionsContainer.innerHTML = `
          <div class="no-sessions">
            <i class="fas fa-info-circle"></i>
            <p>No active sessions</p>
          </div>`;
      } else {
        activeSessionsContainer.innerHTML = activeSessions.map(session =>
          createSessionCard(session, true)
        ).join('');
      }

      // Display finished sessions (last 10 only)
      if (limitedFinishedSessions.length === 0) {
        finishedSessionsContainer.innerHTML = `
          <div class="no-sessions">
            <i class="fas fa-info-circle"></i>
            <p>No completed sessions</p>
          </div>`;
      } else {
        finishedSessionsContainer.innerHTML = limitedFinishedSessions.map(session =>
          createSessionCard(session, false)
        ).join('');
      }

      // Update counts
      document.getElementById('active-count').textContent = activeSessions.length;
      document.getElementById('finished-count').textContent = limitedFinishedSessions.length;

      // Check if we can run more sessions
      updateRunButtonState(activeSessions.length);
    }

    function createSessionCard(session, isActive) {
      const time = new Date(session.created_at);
      const timeStr = time.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

      const statusIcon = getStatusIcon(session.status);
      const statusColor = getStatusColor(session.status);

      const progressBar = isActive && session.progress !== undefined ?
        `<div class="progress-bar">
          <div class="progress-fill" style="width: ${session.progress}%"></div>
          <span class="progress-text">${session.progress}%</span>
        </div>` : '';

      let actionButtons = '';

      if (session.status === 'done' && session.output_file) {
        // Completed session actions
        // Use user copy path for copying, but filename for downloading
        const filename = session.output_file.split('/').pop().split('\\').pop();
        const userCopyPath = session.user_copy_path || session.output_file;

        actionButtons = `
          <div class="session-actions">
            <button class="download-btn" onclick="downloadFile('${filename}'); event.stopPropagation();">
              <i class="fas fa-download"></i> Download
            </button>
            <button class="copy-path-btn" onclick="copyFilePath('${userCopyPath}'); event.stopPropagation();">
              <i class="fas fa-copy"></i> Copy Path
            </button>
          </div>`;
      } else if (session.status === 'running' || session.status === 'pending') {
        // Active session actions
        actionButtons = `
          <div class="session-actions">
            <button class="kill-btn" onclick="killSession('${session.id}'); event.stopPropagation();">
              <i class="fas fa-stop"></i> Kill Session
            </button>
          </div>`;
      } else if (session.status === 'error' || session.status === 'killed') {
        // Error/killed session actions
        actionButtons = `
          <div class="session-actions">
            <button class="retry-btn" onclick="retrySession('${session.id}'); event.stopPropagation();">
              <i class="fas fa-redo"></i> Retry
            </button>
          </div>`;
      }

      return `
        <div class="session-card ${session.status}" data-session-id="${session.id}">
          <div class="session-header">
            <span class="tool-name">${session.tool_name}</span>
            <span class="session-time">${timeStr}</span>
          </div>
          <div class="session-info">
            <span class="session-indicator" style="color: ${statusColor};">
              ${statusIcon}
            </span>
            <span class="session-status">${session.status.toUpperCase()}</span>
            <span class="session-mode">${session.mode}</span>
          </div>
          ${progressBar}
          ${session.error_message ? `<div class="error-message">${session.error_message}</div>` : ''}
          ${actionButtons}
        </div>`;
    }

    function getStatusIcon(status) {
      switch (status) {
        case 'pending': return '<i class="fas fa-clock"></i>';
        case 'running': return '<i class="fas fa-spinner fa-spin"></i>';
        case 'done': return '<i class="fas fa-check-circle"></i>';
        case 'error': return '<i class="fas fa-exclamation-circle"></i>';
        case 'killed': return '<i class="fas fa-stop-circle"></i>';
        default: return '<i class="fas fa-question-circle"></i>';
      }
    }

    function getStatusColor(status) {
      switch (status) {
        case 'pending': return '#ffc107';
        case 'running': return '#17a2b8';
        case 'done': return '#28a745';
        case 'error': return '#dc3545';
        case 'killed': return '#6c757d';
        default: return '#6c757d';
      }
    }

    function updateSessionStats(sessions) {
      const total = Object.keys(sessions).length;
      const completed = Object.values(sessions).filter(s => s.status === 'done').length;
      const failed = Object.values(sessions).filter(s => s.status === 'error').length;

      document.getElementById('total-sessions').textContent = total;
      document.getElementById('completed-sessions').textContent = completed;
      document.getElementById('failed-sessions').textContent = failed;
    }

    function updateRunButtonState(activeSessionCount) {
      const runButton = document.getElementById('run');
      const maxSessions = 3;

      if (activeSessionCount >= maxSessions) {
        runButton.disabled = true;
        runButton.innerHTML = '<i class="fas fa-pause"></i> Session Limit Reached';
        runButton.title = `Maximum ${maxSessions} concurrent sessions allowed`;
        showNotification(`Maximum ${maxSessions} sessions can run concurrently. Please wait for a session to complete.`, 'warning');
      } else {
        runButton.disabled = false;
        runButton.innerHTML = '<i class="fas fa-play"></i> Run Tool';
        runButton.title = '';
      }
    }

    function startSessionPolling() {
      // Only start polling if there are active sessions
      checkAndStartPolling();
    }

    function checkAndStartPolling() {
      loadSessions().then(() => {
        // Check if there are any active sessions
        const activeSessions = document.querySelectorAll('#active-sessions .session-card');

        if (activeSessions.length > 0) {
          // Start polling only if there are active sessions
          if (!sessionUpdateInterval) {
            sessionUpdateInterval = setInterval(() => {
              loadSessions().then(() => {
                // Check again after loading - stop polling if no active sessions
                const activeSessionsAfterUpdate = document.querySelectorAll('#active-sessions .session-card');
                if (activeSessionsAfterUpdate.length === 0) {
                  stopSessionPolling();
                }
              });
            }, 5000);
          }
        } else {
          // No active sessions, stop polling
          stopSessionPolling();
        }
      });
    }

    function stopSessionPolling() {
      if (sessionUpdateInterval) {
        clearInterval(sessionUpdateInterval);
        sessionUpdateInterval = null;
      }
    }

    // Active users management
    let userUpdateInterval = null;

    async function loadActiveUsers() {
      try {
        const response = await fetch('/api/active-users');
        if (response.ok) {
          const users = await response.json();
          displayActiveUsers(users);
          return users;
        } else if (response.status === 401) {
          window.location.href = '/login';
          return null;
        } else {
          console.error('Failed to load active users:', response.statusText);
          return null;
        }
      } catch (error) {
        console.error('Failed to load active users:', error);
        return null;
      }
    }

    function displayActiveUsers(users) {
      const activeUsersContainer = document.getElementById('active-users-list');

      if (users.length === 0) {
        activeUsersContainer.innerHTML = `
          <div class="no-users">
            <i class="fas fa-info-circle"></i>
            <p>No active users</p>
          </div>`;
      } else {
        activeUsersContainer.innerHTML = users.map(user => createUserCard(user)).join('');
      }

      // Update count
      document.getElementById('active-users-count').textContent = users.length;
    }

    function createUserCard(user) {
      const timeAgo = user.minutes_ago === 0 ? 'Just now' :
                     user.minutes_ago === 1 ? '1 minute ago' :
                     `${user.minutes_ago} minutes ago`;

      const statusIcon = user.is_current_user ?
        '<i class="fas fa-user-circle" style="color: #28a745;"></i>' :
        '<i class="fas fa-user"></i>';

      const sessionBadge = user.session_count > 0 ?
        `<span class="session-badge">${user.session_count} sessions</span>` : '';

      return `
        <div class="user-card ${user.is_current_user ? 'current-user' : ''}">
          <div class="user-header">
            ${statusIcon}
            <span class="username">${user.username}${user.is_current_user ? ' (You)' : ''}</span>
            ${sessionBadge}
          </div>
          <div class="user-info">
            <span class="last-activity">${timeAgo}</span>
            <span class="activity-type">${user.activity_type}</span>
          </div>
        </div>`;
    }

    function startUserPolling() {
      // Update active users every 30 seconds
      userUpdateInterval = setInterval(loadActiveUsers, 30000);
    }

    function stopUserPolling() {
      if (userUpdateInterval) {
        clearInterval(userUpdateInterval);
        userUpdateInterval = null;
      }
    }

    // Global function for downloading files
    window.downloadFile = function(filePath) {
      // Extract filename from full path
      const fileName = filePath.split('/').pop().split('\\').pop();

      // Create a temporary link to trigger download
      const link = document.createElement('a');
      link.href = `/download/${fileName}`;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    };

    // Global function for copying file path
    window.copyFilePath = function(filePath) {
      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(filePath).then(() => {
          showNotification('File path copied to clipboard!', 'success');
        }).catch(err => {
          console.error('Failed to copy path:', err);
          fallbackCopyPath(filePath);
        });
      } else {
        fallbackCopyPath(filePath);
      }
    };

    // Fallback copy method for older browsers
    function fallbackCopyPath(filePath) {
      const textArea = document.createElement('textarea');
      textArea.value = filePath;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      try {
        document.execCommand('copy');
        showNotification('File path copied to clipboard!', 'success');
      } catch (err) {
        console.error('Fallback copy failed:', err);
        showNotification('Failed to copy path. Please copy manually: ' + filePath, 'error');
      }

      document.body.removeChild(textArea);
    }

    // Global function for killing sessions
    window.killSession = async function(sessionId) {
      if (!confirm('Are you sure you want to kill this session? This action cannot be undone.')) {
        return;
      }

      try {
        const response = await fetch(`/api/sessions/${sessionId}/kill`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          }
        });

        if (response.ok) {
          const data = await response.json();
          showNotification('Session killed successfully', 'success');
          await loadSessions();
          checkAndStartPolling();
        } else {
          const error = await response.json();
          showNotification(`Failed to kill session: ${error.error}`, 'error');
        }
      } catch (error) {
        console.error('Error killing session:', error);
        showNotification('Network error while killing session', 'error');
      }
    };

    // Global function for retrying sessions
    window.retrySession = function(sessionId) {
      showNotification('Retry functionality coming soon. Please create a new session.', 'info');
    };
    
    // Tab navigation
    navTabs.forEach((tab) => {
      tab.addEventListener('click', () => {
        navTabs.forEach((t) => t.classList.remove('active'));
        tab.classList.add('active');

        const categoryId = tab.getAttribute('data-category');
        displayToolsForCategory(categoryId);
        resetToolSelection();
      });
    });

    function displayToolsForCategory(categoryId) {
      toolsSection.innerHTML = '';

      const categoryTools = toolsData.filter(tool => tool.category === categoryId);

      if (categoryTools.length === 0) {
        toolsSection.innerHTML = `
          <div class="no-tools">
            <i class="fas fa-info-circle"></i>
            <p>No tools available in this category</p>
          </div>`;
        return;
      }

      categoryTools.forEach((tool) => {
        const toolCard = document.createElement('div');
        toolCard.className = 'tool-card box';
        toolCard.innerHTML = `
          <div class="tool-card-header">
            <h3>${tool.name}</h3>
          </div>
          <div class="tool-card-preview">
            ${tool.description.split('\n')[0]}
          </div>
        `;

        toolCard.addEventListener('click', () => selectTool(tool));
        toolsSection.appendChild(toolCard);
      });
    }

    function selectTool(tool) {
      selectedTool = tool;

      // Update tool description
      const toolName = descriptionBox.querySelector('.tool-name');
      const toolId = descriptionBox.querySelector('.tool-id');
      const toolDescription = descriptionBox.querySelector('.tool-description');

      toolName.textContent = tool.name;
      toolId.textContent = tool.id;
      toolDescription.innerHTML = tool.description.replace(/\n/g, '<br>');

      // Show buttons
      dButtons.forEach((button) => {
        button.style.display = 'block';
      });

      // Show mode selection
      document.querySelector('.mode-selection').style.display = 'flex';

      // Highlight selected tool card
      document.querySelectorAll('.tool-card').forEach(card => {
        card.classList.remove('selected');
      });
      event.currentTarget.classList.add('selected');
    }

    function resetToolSelection() {
      selectedTool = null;

      const toolName = descriptionBox.querySelector('.tool-name');
      const toolId = descriptionBox.querySelector('.tool-id');
      const toolDescription = descriptionBox.querySelector('.tool-description');

      toolName.textContent = 'Select a tool to get started';
      toolId.textContent = '';
      toolDescription.textContent = 'Choose a tool from the categories on the left to see its description and configuration options.';

      // Hide buttons
      dButtons.forEach((button) => {
        button.style.display = 'none';
      });

      // Hide mode selection
      document.querySelector('.mode-selection').style.display = 'none';

      // Reset file selection
      uploadedFile = null;
      fileInput.value = '';
      fileInfo.style.display = 'none';
    }

    // Run Button Click Handler
    runButton.addEventListener('click', async () => {
      if (!uploadedFile) {
        showNotification('Please upload a file', 'error');
        return;
      }

      if (!selectedTool) {
        showNotification('Please select a tool', 'error');
        return;
      }

      // Check session limit
      const activeSessions = document.querySelectorAll('#active-sessions .session-card');
      if (activeSessions.length >= 3) {
        showNotification('Maximum 3 sessions can run concurrently. Please wait for a session to complete.', 'warning');
        return;
      }

      const formData = new FormData();
      formData.append('file', uploadedFile);
      formData.append('tool', selectedTool.toolName || selectedTool.name.toLowerCase().replace(/\s+/g, '_'));
      formData.append('mode', modeButton.checked ? 'fast' : 'normal');

      try {
        // Disable button during processing
        runButton.disabled = true;
        runButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';

        // Upload file and create session
        const uploadResponse = await fetch('/upload', {
          method: 'POST',
          body: formData,
        });

        if (!uploadResponse.ok) {
          throw new Error('Upload failed');
        }

        const uploadData = await uploadResponse.json();
        currentSessionId = uploadData.session_id;

        showNotification('File uploaded successfully. Starting processing...', 'success');

        // Start tool processing
        const runResponse = await fetch('/runtool', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ session_id: currentSessionId })
        });

        if (!runResponse.ok) {
          throw new Error('Failed to start processing');
        }

        const runData = await runResponse.json();
        showNotification('Processing started successfully', 'success');

        // Reset form
        uploadedFile = null;
        fileInput.value = '';
        fileInfo.style.display = 'none';

        // Refresh sessions immediately and restart polling
        await loadSessions();
        checkAndStartPolling();

      } catch (error) {
        console.error('Error processing file:', error);
        showNotification('Error: ' + error.message, 'error');
      } finally {
        // Re-enable button
        runButton.disabled = false;
        runButton.innerHTML = '<i class="fas fa-play"></i> Run Tool';
      }
    });

    // Download sample button handler
    downloadSampleButton.addEventListener('click', () => {
      if (!selectedTool) {
        showNotification('Please select a tool first', 'error');
        return;
      }

      // For now, show a message about sample files
      showNotification(`Sample file for ${selectedTool.name} would be downloaded here`, 'info');
    });

    // Notification system
    function showNotification(message, type = 'info') {
      const notification = document.createElement('div');
      notification.className = `notification ${type}`;
      notification.innerHTML = `
        <i class="fas fa-${getNotificationIcon(type)}"></i>
        <span>${message}</span>
        <button class="close-btn" onclick="this.parentElement.remove()">
          <i class="fas fa-times"></i>
        </button>
      `;

      document.body.appendChild(notification);

      // Auto-remove after 5 seconds
      setTimeout(() => {
        if (notification.parentElement) {
          notification.remove();
        }
      }, 5000);
    }

    function getNotificationIcon(type) {
      switch (type) {
        case 'success': return 'check-circle';
        case 'error': return 'exclamation-circle';
        case 'warning': return 'exclamation-triangle';
        default: return 'info-circle';
      }
    }

    // Initialize the application
    initializeApp();
  </script>
</body>
</html>
