/* Cosmic Space Theme - Deep space interface with stellar backgrounds and cosmic aesthetics
 * Design Concept: Space-inspired dark theme with cosmic backgrounds, stellar effects, and astronaut elements
 * Key Features: 
 *   - Deep space backgrounds with cosmic imagery
 *   - Glassmorphism effects with stellar blur
 *   - Blue space color palette with cosmic accents
 *   - Astronaut and space iconography
 *   - Stellar particle effects and cosmic animations
 * Color Palette: Deep space blacks with cosmic blues (#0f33ff, aliceblue)
 * Typography: Montserrat with space-age styling and cosmic letter spacing
 */

:root {
  /* Cosmic Colors - Deep Space Palette */
  --color-bg: #000000;
  --color-text: aliceblue;
  --color-text-secondary: rgba(240, 248, 255, 0.7);
  --color-border: rgba(255, 255, 255, 0.2);
  --color-white: rgba(32, 32, 32, 0.05);
  --color-accent: #0f33ff;
  --color-card-bg: transparent;
  --color-card-border: aliceblue;

  /* Cosmic Gradients */
  --gradient-cosmic: linear-gradient(135deg, #000428 0%, #004e92 100%);
  --gradient-nebula: linear-gradient(45deg, #0f33ff 0%, #1a1a2e 50%, #16213e 100%);
  --gradient-stellar: radial-gradient(circle, rgba(15, 51, 255, 0.3) 0%, transparent 70%);

  /* Spacing System */
  --spacing-xs: 5px;
  --spacing-sm: 8px;
  --spacing-md: 10px;
  --spacing-lg: 15px;
  --spacing-xl: 20px;
  --spacing-xxl: 30px;

  /* Typography */
  --font-size-xs: 10px;
  --font-size-sm: 12px;
  --font-size-md: 14px;
  --font-size-lg: 18px;
  --font-size-xl: 24px;
  --font-size-xxl: 42px;

  /* Layout */
  --border-standard: 1px solid var(--color-border);
  --box-padding: var(--spacing-lg);
  --letter-spacing-standard: 0.5px;
  --letter-spacing-wide: 5px;
  --section-gap: 0;
  --card-gap: var(--spacing-md);
  --box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
  --border-radius-sm: 2px;
  --backdrop-blur: blur(20px);

  /* Cosmic Effects */
  --cosmic-glow: 0 0 20px rgba(15, 51, 255, 0.5);
  --stellar-shadow: 0 8px 32px rgba(15, 51, 255, 0.3);
  --nebula-blur: blur(15px);
}

* {
  box-sizing: border-box;
  font-family: 'Montserrat', sans-serif;
  margin: 0;
  padding: 0;
}

body {
  background: var(--color-bg);
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(15, 51, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(15, 51, 255, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(15, 51, 255, 0.05) 0%, transparent 50%);
  background-attachment: fixed;
  color: var(--color-text);
  line-height: 1.6;
  margin: 0;
  min-height: 100vh;
  display: flex;
  flex-direction: row;
  overflow-x: hidden;
  position: relative;
}

/* Cosmic Background Animation */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(2px 2px at 20px 30px, #fff, transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
    radial-gradient(1px 1px at 90px 40px, #fff, transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
    radial-gradient(2px 2px at 160px 30px, #fff, transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: cosmicDrift 20s linear infinite;
  opacity: 0.3;
  z-index: -1;
}

@keyframes cosmicDrift {
  0% { transform: translateX(0px); }
  100% { transform: translateX(-200px); }
}

.side-bar-container {
  display: flex;
  height: 100vh;
  width: 100px;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl) 0;
}

.side-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xxl);
  padding: var(--spacing-xxl);
  width: 60px;
  height: 50vh;
  border-radius: var(--border-radius-sm);
  background: var(--color-white);
  backdrop-filter: var(--backdrop-blur);
  -webkit-backdrop-filter: var(--backdrop-blur);
  box-shadow: var(--stellar-shadow);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: var(--border-standard);
  position: relative;
}

.side-bar::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: var(--gradient-nebula);
  border-radius: var(--border-radius-sm);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.side-bar:hover {
  background: rgba(32, 32, 32, 0.1);
  box-shadow: var(--cosmic-glow);
  transform: translateY(-2px) scale(1.02);
}

.side-bar:hover::before {
  opacity: 0.3;
}

.tool-category {
  color: var(--color-text);
  display: flex;
  padding: var(--spacing-md);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: var(--color-white);
  backdrop-filter: var(--nebula-blur);
  -webkit-backdrop-filter: var(--nebula-blur);
  position: relative;
  overflow: hidden;
  border: 1px solid var(--color-border);
  width: 48px;
  height: 48px;
  align-items: center;
  justify-content: center;
}

.tool-category::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-stellar);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
  border-radius: 50%;
}

.tool-category:hover {
  background: rgba(32, 32, 32, 0.15);
  color: var(--color-text);
  transform: scale(1.1) rotate(5deg);
  box-shadow: var(--cosmic-glow);
  text-shadow: 0 0 10px currentColor;
}

.tool-category:hover::before {
  opacity: 1;
}

.tool-category[aria-selected="true"] {
  background: var(--color-accent);
  color: var(--color-text);
  box-shadow: var(--cosmic-glow);
  text-shadow: 0 0 15px currentColor;
}

.main-content {
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  padding: var(--spacing-xl);
  gap: var(--spacing-xl);
}

.tools-cards-section {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: var(--spacing-md);
  border-radius: var(--border-radius-sm);
  overflow-y: auto;
  max-width: 270px;
}

.tools-cards-section h2 {
  align-self: flex-start;
  margin-bottom: var(--spacing-md);
  color: var(--color-text);
  font-size: var(--font-size-lg);
  font-weight: 700;
  text-shadow: 0 0 10px currentColor;
  letter-spacing: var(--letter-spacing-standard);
}

.tools-cards-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.middle-section {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: var(--spacing-xxl);
  overflow-y: auto;
}

.sessions-section {
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  padding: var(--spacing-xxl);
  overflow-y: auto;
  gap: var(--spacing-xl);
}

.finished-sessions, .running-sessions {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: var(--spacing-xxl);
  background: var(--color-white);
  backdrop-filter: var(--backdrop-blur);
  -webkit-backdrop-filter: var(--backdrop-blur);
  border-radius: var(--border-radius-sm);
  box-shadow: var(--stellar-shadow);
  overflow-y: auto;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: var(--border-standard);
  position: relative;
}

.finished-sessions::before, .running-sessions::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-stellar);
  border-radius: var(--border-radius-sm);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.finished-sessions:hover, .running-sessions:hover {
  background: rgba(32, 32, 32, 0.1);
  transform: translateY(-2px);
  box-shadow: var(--cosmic-glow);
}

.finished-sessions:hover::before, .running-sessions:hover::before {
  opacity: 0.3;
}

/* Typography Styles */
h1, h2, h3, h4, h5, h6 {
  color: var(--color-text);
  font-weight: 700;
  margin-bottom: var(--spacing-xl);
  text-shadow: 0 0 15px currentColor;
  letter-spacing: var(--letter-spacing-standard);
}

h1 {
  font-size: var(--font-size-xxl);
  background: var(--gradient-nebula);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: cosmicPulse 3s ease-in-out infinite alternate;
}

@keyframes cosmicPulse {
  from { filter: drop-shadow(0 0 10px var(--color-accent)); }
  to { filter: drop-shadow(0 0 20px var(--color-text)); }
}

h2 {
  font-size: var(--font-size-xl);
  color: var(--color-text);
}

h3 {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
}

/* AutoSpace Section */
.autospace-section {
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-xxl);
  margin-bottom: var(--spacing-xl);
}

.default-view {
  color: var(--color-text-secondary);
  font-size: var(--font-size-lg);
  text-align: center;
  padding: 40px var(--spacing-xl);
  background: var(--color-white);
  backdrop-filter: var(--backdrop-blur);
  -webkit-backdrop-filter: var(--backdrop-blur);
  border-radius: var(--border-radius-sm);
  font-style: italic;
  border: var(--border-standard);
  box-shadow: var(--stellar-shadow);
  text-shadow: 0 0 10px currentColor;
}

.selection-view {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
  background: var(--color-white);
  backdrop-filter: var(--backdrop-blur);
  -webkit-backdrop-filter: var(--backdrop-blur);
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-xxl);
  border: var(--border-standard);
  box-shadow: var(--stellar-shadow);
  position: relative;
}

.selection-view::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-stellar);
  border-radius: var(--border-radius-sm);
  opacity: 0.1;
  z-index: -1;
}

.tool-name {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--color-accent);
  margin-bottom: var(--spacing-md);
  text-shadow: 0 0 15px currentColor;
  letter-spacing: var(--letter-spacing-standard);
}

.tool-description {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  line-height: 1.6;
  margin-bottom: var(--spacing-xl);
  text-shadow: 0 0 5px currentColor;
}

.tool-buttons {
  display: flex;
  flex-direction: row;
  gap: var(--spacing-lg);
  align-items: flex-start;
}

/* Button Styles - Cosmic Design */
.run-button, .download-sample-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg) var(--spacing-xxl);
  font-size: var(--font-size-md);
  font-weight: 700;
  color: var(--color-text);
  background: var(--color-white);
  backdrop-filter: var(--backdrop-blur);
  -webkit-backdrop-filter: var(--backdrop-blur);
  border: 2px solid var(--color-accent);
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-standard);
  box-shadow: var(--stellar-shadow);
  position: relative;
  overflow: hidden;
  text-shadow: 0 0 10px currentColor;
}

.run-button::before, .download-sample-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-nebula);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.run-button:hover, .download-sample-button:hover {
  background: rgba(32, 32, 32, 0.15);
  transform: translateY(-3px) scale(1.05);
  box-shadow: var(--cosmic-glow);
  color: var(--color-text);
  border-color: var(--color-text);
  text-shadow: 0 0 15px currentColor;
}

.run-button:hover::before, .download-sample-button:hover::before {
  opacity: 0.3;
}

.run-button:active, .download-sample-button:active {
  transform: translateY(-1px) scale(1.02);
}

/* File Input Styles */
.file-label {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg) var(--spacing-xxl);
  font-size: var(--font-size-md);
  font-weight: 700;
  color: var(--color-text);
  background: var(--color-white);
  backdrop-filter: var(--backdrop-blur);
  -webkit-backdrop-filter: var(--backdrop-blur);
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-standard);
  box-shadow: var(--stellar-shadow);
  position: relative;
  overflow: hidden;
  border: 2px solid var(--color-text-secondary);
  text-shadow: 0 0 10px currentColor;
}

.file-label::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-stellar);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.file-label:hover {
  background: rgba(32, 32, 32, 0.15);
  transform: translateY(-3px) scale(1.05);
  box-shadow: var(--cosmic-glow);
  color: var(--color-accent);
  text-shadow: 0 0 15px currentColor;
}

.file-label:hover::before {
  opacity: 0.3;
}

#fileInput {
  display: none;
}

/* Checkbox and Label Styles */
input[type="checkbox"] {
  appearance: none;
  width: 20px;
  height: 20px;
  background: var(--color-white);
  backdrop-filter: var(--nebula-blur);
  -webkit-backdrop-filter: var(--nebula-blur);
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-right: var(--spacing-md);
  border: 2px solid var(--color-text-secondary);
  box-shadow: 0 0 10px var(--color-text-secondary);
}

input[type="checkbox"]:checked {
  background: var(--color-accent);
  box-shadow: var(--cosmic-glow);
  border-color: var(--color-accent);
}

input[type="checkbox"]:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--color-text);
  font-weight: bold;
  font-size: 14px;
  text-shadow: 0 0 10px currentColor;
}

label[for="mode"] {
  color: var(--color-text-secondary);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-shadow: 0 0 5px currentColor;
  letter-spacing: var(--letter-spacing-standard);
}

label[for="mode"]:hover {
  color: var(--color-accent);
  text-shadow: 0 0 10px currentColor;
}

/* Right Section Styles */
.right-section {
  display: flex;
  flex-direction: column;
  width: 350px;
  gap: var(--spacing-xl);
  flex-grow: .5;
}

.system-performance-section, .active-users-section {
  padding: var(--spacing-xxl);
}

.box-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-size: var(--font-size-lg);
  font-weight: 700;
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-xl);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-standard);
  text-shadow: 0 0 15px currentColor;
}

.box-title i {
  color: var(--color-accent);
  font-size: var(--font-size-xl);
  text-shadow: 0 0 15px currentColor;
}

.performance-box, .users-box {
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-xl);
}

.performance-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
}

.metric-card {
  background: var(--color-white);
  backdrop-filter: var(--nebula-blur);
  -webkit-backdrop-filter: var(--nebula-blur);
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 0 15px rgba(15, 51, 255, 0.2);
  border: 1px solid var(--color-accent);
  position: relative;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-stellar);
  border-radius: var(--border-radius-sm);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.metric-card:hover {
  background: rgba(32, 32, 32, 0.15);
  transform: translateY(-2px);
  box-shadow: var(--cosmic-glow);
}

.metric-card:hover::before {
  opacity: 0.3;
}

.metric-icon i {
  color: var(--color-accent);
  font-size: var(--font-size-xl);
  text-shadow: 0 0 15px currentColor;
}

.metric-content {
  display: flex;
  flex-direction: column;
}

.metric-label {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-standard);
  text-shadow: 0 0 5px currentColor;
}

.metric-value {
  font-size: var(--font-size-lg);
  color: var(--color-text);
  font-weight: 700;
  text-shadow: 0 0 10px currentColor;
}

/* User Count Badge */
.user-count {
  background: var(--color-accent);
  color: var(--color-text);
  padding: 4px 12px;
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-sm);
  font-weight: 700;
  margin-left: auto;
  text-shadow: 0 0 10px currentColor;
  box-shadow: var(--cosmic-glow);
  letter-spacing: var(--letter-spacing-standard);
}

/* Users Container */
.users-container {
  max-height: 300px;
  overflow-y: auto;
}

.no-users {
  text-align: center;
  padding: var(--spacing-xxl) var(--spacing-xl);
  color: var(--color-text-secondary);
  text-shadow: 0 0 10px currentColor;
}

.no-users i {
  font-size: var(--font-size-xl);
  margin-bottom: var(--spacing-md);
  color: var(--color-accent);
  text-shadow: 0 0 15px currentColor;
}

.user-card {
  background: var(--color-white);
  backdrop-filter: var(--nebula-blur);
  -webkit-backdrop-filter: var(--nebula-blur);
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.2);
  border: 1px solid var(--color-text-secondary);
  position: relative;
}

.user-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-stellar);
  border-radius: var(--border-radius-sm);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.user-card:hover {
  background: rgba(32, 32, 32, 0.15);
  transform: translateX(5px);
  box-shadow: 0 0 25px rgba(0, 255, 255, 0.4);
}

.user-card:hover::before {
  opacity: 0.2;
}

.user-card.current-user {
  background: linear-gradient(135deg, var(--color-white), rgba(15, 51, 255, 0.3));
  box-shadow: var(--cosmic-glow);
  border-color: var(--color-accent);
}

.user-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
}

.user-header i {
  color: var(--color-accent);
  font-size: var(--font-size-lg);
  text-shadow: 0 0 10px currentColor;
}

.username {
  color: var(--color-text);
  font-weight: 700;
  flex-grow: 1;
  text-shadow: 0 0 10px currentColor;
  letter-spacing: var(--letter-spacing-standard);
}

.session-badge {
  background: var(--color-text-secondary);
  color: var(--color-text);
  padding: 2px 8px;
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 700;
  text-shadow: 0 0 5px currentColor;
  box-shadow: 0 0 10px var(--color-text-secondary);
}

.user-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-xs);
}

.last-activity {
  color: var(--color-text-secondary);
  font-style: italic;
  text-shadow: 0 0 5px currentColor;
}

.activity-type {
  color: var(--color-text-secondary);
  background: var(--color-white);
  padding: 2px 8px;
  border-radius: var(--border-radius-sm);
  font-weight: 500;
  text-shadow: 0 0 5px currentColor;
  border: 1px solid var(--color-accent);
}

/* Tool Cards Styles */
.tool-card {
  background: var(--color-white);
  backdrop-filter: var(--backdrop-blur);
  -webkit-backdrop-filter: var(--backdrop-blur);
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 0 20px rgba(15, 51, 255, 0.2);
  position: relative;
  overflow: hidden;
  border: 2px solid var(--color-accent);
}

.tool-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-nebula);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.tool-card:hover {
  background: rgba(32, 32, 32, 0.15);
  transform: translateY(-5px) scale(1.02);
  box-shadow: var(--cosmic-glow);
}

.tool-card:hover::before {
  opacity: 0.2;
}

.tool-card h3 {
  color: var(--color-accent);
  font-size: var(--font-size-lg);
  font-weight: 700;
  margin-bottom: var(--spacing-md);
  text-shadow: 0 0 15px currentColor;
  letter-spacing: var(--letter-spacing-standard);
}

.tool-card p {
  color: var(--color-text-secondary);
  font-size: var(--font-size-md);
  line-height: 1.5;
  text-shadow: 0 0 5px currentColor;
}

/* Session Cards Styles */
.session-card {
  background: var(--color-white);
  backdrop-filter: var(--backdrop-blur);
  -webkit-backdrop-filter: var(--backdrop-blur);
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  border: 1px solid var(--color-text-secondary);
}

.session-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--color-text-secondary);
  transition: all 0.3s ease;
  box-shadow: 0 0 10px var(--color-text-secondary);
}

.session-card:hover {
  background: rgba(32, 32, 32, 0.15);
  transform: translateX(5px);
  box-shadow: 0 0 30px rgba(0, 255, 255, 0.4);
}

.session-card:hover::before {
  width: 6px;
  background: var(--color-accent);
  box-shadow: var(--cosmic-glow);
}

.session-card .title {
  color: var(--color-text);
  font-size: var(--font-size-lg);
  font-weight: 700;
  margin-bottom: var(--spacing-md);
  text-shadow: 0 0 10px currentColor;
  letter-spacing: var(--letter-spacing-standard);
}

.session-card .time {
  color: var(--color-text-secondary);
  font-size: var(--font-size-xs);
  background: var(--color-white);
  padding: 4px 10px;
  border-radius: var(--border-radius-sm);
  display: inline-block;
  margin-bottom: var(--spacing-md);
  text-shadow: 0 0 5px currentColor;
  border: 1px solid var(--color-accent);
}

.progress-counter {
  color: var(--color-accent);
  font-weight: 700;
  font-size: var(--font-size-md);
  background: var(--color-white);
  padding: 4px 10px;
  border-radius: var(--border-radius-sm);
  display: inline-block;
  margin-bottom: var(--spacing-md);
  text-shadow: 0 0 10px currentColor;
  border: 1px solid var(--color-accent);
  letter-spacing: var(--letter-spacing-standard);
}

/* Session Action Buttons */
.refresh-button, .kill-button, .download-button {
  background: var(--color-white);
  backdrop-filter: var(--nebula-blur);
  -webkit-backdrop-filter: var(--nebula-blur);
  color: var(--color-text);
  border: 2px solid var(--color-text-secondary);
  padding: 8px 16px;
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-right: var(--spacing-sm);
  margin-top: var(--spacing-sm);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-standard);
  text-shadow: 0 0 10px currentColor;
}

.refresh-button:hover {
  background: var(--color-text-secondary);
  transform: translateY(-2px);
  box-shadow: 0 0 20px var(--color-text-secondary);
  color: var(--color-text);
}

.kill-button:hover {
  background: #ff0040;
  border-color: #ff0040;
  transform: translateY(-2px);
  box-shadow: 0 0 20px #ff0040;
  color: var(--color-text);
}

.download-button:hover {
  background: #00ff80;
  border-color: #00ff80;
  transform: translateY(-2px);
  box-shadow: 0 0 20px #00ff80;
  color: var(--color-bg);
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-white);
  border-radius: var(--border-radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--color-text-secondary);
  border-radius: var(--border-radius-sm);
  transition: all 0.2s ease;
  box-shadow: 0 0 10px var(--color-text-secondary);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-accent);
  box-shadow: var(--cosmic-glow);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
  }

  .right-section {
    width: 100%;
    flex-direction: row;
  }

  .system-performance-section, .active-users-section {
    flex: 1;
  }
}

@media (max-width: 768px) {
  .side-bar-container {
    width: 80px;
  }

  .main-content {
    padding: var(--spacing-lg);
  }

  .right-section {
    flex-direction: column;
  }

  .performance-metrics {
    grid-template-columns: 1fr;
  }
}
