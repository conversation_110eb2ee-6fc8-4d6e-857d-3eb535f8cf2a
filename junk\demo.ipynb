{"cells": [{"cell_type": "code", "execution_count": null, "id": "b215275a", "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "12235", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                  <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[7]\u001b[39m\u001b[32m, line 12\u001b[39m\n\u001b[32m     10\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mopen\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33msessions_data/sessions.json\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mr\u001b[39m\u001b[33m\"\u001b[39m) \u001b[38;5;28;01mas\u001b[39;00m sessions_log_file:\n\u001b[32m     11\u001b[39m     data = json.load(sessions_log_file)\n\u001b[32m---> \u001b[39m\u001b[32m12\u001b[39m     \u001b[43mdata\u001b[49m\u001b[43m[\u001b[49m\u001b[32;43m12235\u001b[39;49m\u001b[43m]\u001b[49m[\u001b[33m\"\u001b[39m\u001b[33mprogress\u001b[39m\u001b[33m\"\u001b[39m] = \u001b[32m50\u001b[39m\n\u001b[32m     14\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mopen\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33msessions_data/sessions.json\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mw\u001b[39m\u001b[33m\"\u001b[39m) \u001b[38;5;28;01mas\u001b[39;00m sessions_log_file:\n\u001b[32m     15\u001b[39m     json.dump(data, sessions_log_file, indent=\u001b[32m4\u001b[39m)\n", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m: 12235"]}], "source": ["import json\n", "with open(\"sessions_data/sessions.json\", \"r\") as sessions_log_file:\n", "    data = json.load(sessions_log_file)\n", "    data[12235] = {\"status\":\"running\", \"progress\": 0}\n", "\n", "with open(\"sessions_data/sessions.json\", \"w\") as sessions_log_file:\n", "    json.dump(data, sessions_log_file, indent=4)\n", "\n", "\n", "with open(\"sessions_data/sessions.json\", \"r\") as sessions_log_file:\n", "    data = json.load(sessions_log_file)\n", "    data[\"12235\"][\"progress\"] = 50\n", "\n", "with open(\"sessions_data/sessions.json\", \"w\") as sessions_log_file:\n", "    json.dump(data, sessions_log_file, indent=4)\n"]}, {"cell_type": "code", "execution_count": null, "id": "cf0a043d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Folder: C:\\D_Partition\\Compare\\Work\n", "  2023_Backlog.xlsx\n", "  2023_backlog_23-4.xlsx\n", "  ab<PERSON><PERSON><PERSON>_ma<PERSON><PERSON><PERSON>-Export_By_Original_Latest-19-02-2025--05-08-22.txt\n", "  Backlog_10-2.xlsx\n", "  Backlog_12-1.xlsx\n", "  Backlog_12-5.xlsx\n", "  Backlog_14-1.xlsx\n", "  Backlog_15-1.xlsx\n", "  Backlog_16-1.xlsx\n", "  Backlog_18-2.xlsx\n", "  Backlog_19-1.xlsx\n", "  Backlog_2-2.xlsx\n", "  Backlog_2-3.xlsx\n", "  Backlog_2024-8-1.xlsx\n", "  Backlog_2024_5-1.xlsx\n", "  Backlog_21-1.xlsx\n", "  Backlog_22-1.xlsx\n", "  Backlog_26-1.xlsx\n", "  Backlog_26-2.xlsx\n", "  Backlog_26-3.xlsx\n", "  Backlog_28-1.xlsx\n", "  Backlog_3-2.xlsx\n", "  Backlog_4-3.xlsx\n", "  Backlog_5-2.xlsx\n", "  Backlog_6-2.xlsx\n", "  Backlog_6-4.xlsx\n", "  Backlog_finished_13-5.xlsx\n", "  Backlog_Mapping_21-1.xlsx\n", "  Book12.xlsx\n", "  Book3.xlsx\n", "  Book8.xlsx\n", "  Fast_Data_Review.xlsx\n", "  PC_PR_15-7.xlsx\n", "  PR_12-5.xlsx\n", "  PR_19-2.xlsx\n", "  PR_23-3.xlsx\n", "  PR_23-4.xlsx\n", "  PR_3-4.xlsx\n", "  PR_8-4(1).xlsx\n", "  PR_8-4.xlsx\n", "  sample.xlsx\n", "  SSS.xlsx\n", "  Stragetic_17-2.xlsx\n", "  Tools_12-2.xlsx\n", "  Tools_13-4.xlsx\n", "  Tools_14-1.xlsx\n", "  Tools_20-3.xlsx\n", "  Total_PR.xlsx\n"]}], "source": ["import os\n", "\n", "def read_all_folders():\n", "    folder = r\"C:\\D_Partition\\Compare\\Work\"\n", "    \n", "    for root, dirs, files in os.walk(folder):\n", "        print(f\"Folder: {root}\")\n", "        for file in files:\n", "            print(f\"  {file}\")\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "7e228755", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Folder: C:\\D_Partition\\Compare\\Work\n", "  2023_Backlog.xlsx\n", "  2023_backlog_23-4.xlsx\n", "  ab<PERSON><PERSON><PERSON>_ma<PERSON><PERSON><PERSON>-Export_By_Original_Latest-19-02-2025--05-08-22.txt\n", "  Backlog_10-2.xlsx\n", "  Backlog_12-1.xlsx\n", "  Backlog_12-5.xlsx\n", "  Backlog_14-1.xlsx\n", "  Backlog_15-1.xlsx\n", "  Backlog_16-1.xlsx\n", "  Backlog_18-2.xlsx\n", "  Backlog_19-1.xlsx\n", "  Backlog_2-2.xlsx\n", "  Backlog_2-3.xlsx\n", "  Backlog_2024-8-1.xlsx\n", "  Backlog_2024_5-1.xlsx\n", "  Backlog_21-1.xlsx\n", "  Backlog_22-1.xlsx\n", "  Backlog_26-1.xlsx\n", "  Backlog_26-2.xlsx\n", "  Backlog_26-3.xlsx\n", "  Backlog_28-1.xlsx\n", "  Backlog_3-2.xlsx\n", "  Backlog_4-3.xlsx\n", "  Backlog_5-2.xlsx\n", "  Backlog_6-2.xlsx\n", "  Backlog_6-4.xlsx\n", "  Backlog_finished_13-5.xlsx\n", "  Backlog_Mapping_21-1.xlsx\n", "  Book12.xlsx\n", "  Book3.xlsx\n", "  Book8.xlsx\n", "  Fast_Data_Review.xlsx\n", "  PC_PR_15-7.xlsx\n", "  PR_12-5.xlsx\n", "  PR_19-2.xlsx\n", "  PR_23-3.xlsx\n", "  PR_23-4.xlsx\n", "  PR_3-4.xlsx\n", "  PR_8-4(1).xlsx\n", "  PR_8-4.xlsx\n", "  sample.xlsx\n", "  SSS.xlsx\n", "  Stragetic_17-2.xlsx\n", "  Tools_12-2.xlsx\n", "  Tools_13-4.xlsx\n", "  Tools_14-1.xlsx\n", "  Tools_20-3.xlsx\n", "  Total_PR.xlsx\n", "\n"]}], "source": ["import subprocess\n", "import sys\n", "\n", "process_1 = subprocess.Popen(\n", "    [sys.executable, \"function.py\"],\n", "    capture_output=True,\n", "    text=True\n", ")"]}, {"cell_type": "code", "execution_count": 2, "id": "abb108c6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["17:43\n"]}], "source": ["import time\n", "print(time.strftime(\"%H:%M\"))\n"]}, {"cell_type": "code", "execution_count": 10, "id": "f8c047f7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'T101': [{'Name': 'Equal Manual', 'Description': nan, 'Tool_Name': 'equal_manual', 'Category': 'COMPARISON', 'Category_ID': 1}], 'T102': [{'Name': 'Image Compare', 'Description': nan, 'Tool_Name': 'image_compare', 'Category': 'COMPARISON', 'Category_ID': 1}], 'T103': [{'Name': 'RB Line Doc', 'Description': nan, 'Tool_Name': 'rb_line_doc', 'Category': 'COMPARISON', 'Category_ID': 1}], 'T104': [{'Name': 'Page Compare', 'Description': nan, 'Tool_Name': 'page_compare', 'Category': 'COMPARISON', 'Category_ID': 1}], 'T201': [{'Name': 'Extractor', 'Description': nan, 'Tool_Name': 'extractor', 'Category': 'EXTRACTION', 'Category_ID': 2}], 'T202': [{'Name': 'Metadata', 'Description': nan, 'Tool_Name': 'metadata', 'Category': 'EXTRACTION', 'Category_ID': 2}], 'T301': [{'Name': 'Keyword Search', 'Description': nan, 'Tool_Name': 'keyword_search', 'Category': 'SEARCHING', 'Category_ID': 3}], 'T302': [{'Name': 'Part Search', 'Description': nan, 'Tool_Name': 'part_search', 'Category': 'SEARCHING', 'Category_ID': 3}]}\n"]}], "source": ["import pandas as pd\n", "df = pd.read_excel(\"tools_data.xlsx\")\n", "\n", "\n", "tools_dict = {}\n", "for _, row in df.iterrows():\n", "    tool_id = row['ID']\n", "    if tool_id not in tools_dict:\n", "        tools_dict[tool_id] = []\n", "    \n", "    tool_info = {\n", "        'Name': row['Name'],\n", "        'Description': row['Description'],\n", "        'Tool_Name': row['Tool_Name'],\n", "        \"Category\": row['Category'],\n", "        'Category_ID': row['Category_ID']\n", "    }\n", "    tools_dict[tool_id].append(tool_info)\n", "\n", "print(tools_dict)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.1"}}, "nbformat": 4, "nbformat_minor": 5}