/* Corporate Professional Theme - Enterprise-grade design with unified system and professional aesthetics
 * Design Concept: Clean, professional interface inspired by enterprise dashboards with systematic design tokens
 * Key Features: 
 *   - Unified design system with comprehensive variables
 *   - Professional color palette with blue gradients
 *   - Systematic spacing and typography scales
 *   - Grid-based layout with consistent components
 *   - Enterprise-grade visual hierarchy
 * Color Palette: Professional blues (#667eea, #764ba2) with neutral grays
 * Typography: Montserrat font family with systematic weight and size scales
 */

:root {
  /* Primary Colors - Professional Blue Palette */
  --color-primary: #667eea;
  --color-primary-dark: #5a6fd8;
  --color-accent: #764ba2;
  --color-accent-dark: #6a4190;

  /* Background Colors */
  --color-bg: #f5f5f5;
  --color-bg-secondary: #f8f9ff;
  --color-bg-tertiary: #e8f0ff;
  --color-white: #fff;

  /* Text Colors */
  --color-text: #2a2a2a;
  --color-text-secondary: #666;
  --color-text-light: #999;
  --color-text-white: #fff;

  /* Border and Shadow */
  --color-border: #e0e0e0;
  --color-border-light: #f0f0f0;

  /* Status Colors */
  --color-success: #28a745;
  --color-error: #dc3545;
  --color-warning: #ffc107;
  --color-info: #17a2b8;

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--color-primary), var(--color-accent));
  --gradient-auth: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-card: linear-gradient(135deg, var(--color-bg-secondary), var(--color-bg-tertiary));

  /* Spacing System */
  --spacing-xs: 5px;
  --spacing-sm: 8px;
  --spacing-md: 10px;
  --spacing-lg: 15px;
  --spacing-xl: 20px;
  --spacing-xxl: 30px;
  --spacing-3xl: 40px;
  --spacing-4xl: 60px;

  /* Typography Scale */
  --font-size-xs: 10px;
  --font-size-sm: 12px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 24px;
  --font-size-3xl: 32px;
  --font-size-4xl: 42px;

  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Border Radius */
  --border-radius-sm: 2px;
  --border-radius-md: 5px;
  --border-radius-lg: 10px;
  --border-radius-xl: 15px;
  --border-radius-2xl: 20px;
  --border-radius-pill: 25px;

  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 4px 8px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 8px 16px rgba(0, 0, 0, 0.1);
  --shadow-2xl: 0 20px 40px rgba(0, 0, 0, 0.2);

  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;

  /* Layout */
  --border-standard: 1px solid var(--color-border);
  --nav-height: 60px;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Montserrat', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
  background-color: var(--color-bg);
  color: var(--color-text);
  line-height: 1.6;
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-normal);
  margin: 0;
  display: flex;
  flex-direction: row;
  min-height: 100vh;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.side-bar-container {
  display: flex;
  height: 100vh;
  width: 100px;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl) 0;
  background: var(--color-white);
  border-right: var(--border-standard);
}

.side-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-3xl);
  padding: var(--spacing-xxl) var(--spacing-lg);
  width: 70px;
  height: auto;
  min-height: 50vh;
  border-radius: var(--border-radius-lg);
  background: var(--color-white);
  box-shadow: var(--shadow-lg);
  transition: var(--transition-normal);
  border: var(--border-standard);
}

.side-bar:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-2px);
}

.tool-category {
  color: var(--color-text);
  display: flex;
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: var(--transition-normal);
  background: transparent;
  position: relative;
  width: 48px;
  height: 48px;
  align-items: center;
  justify-content: center;
  border: 2px solid transparent;
}

.tool-category:hover {
  background: var(--color-bg-secondary);
  color: var(--color-primary);
  transform: scale(1.05);
  border-color: var(--color-border);
}

.tool-category[aria-selected="true"] {
  background: var(--gradient-primary);
  color: var(--color-text-white);
  box-shadow: var(--shadow-md);
  border-color: var(--color-primary);
}

.main-content {
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  padding: var(--spacing-xl);
  gap: var(--spacing-xl);
  background: var(--color-bg);
}

.tools-cards-section {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: var(--spacing-md);
  border-radius: var(--border-radius-lg);
  overflow-y: auto;
  max-width: 270px;
  background: var(--color-white);
  border: var(--border-standard);
  box-shadow: var(--shadow-md);
}

.tools-cards-section h2 {
  align-self: flex-start;
  margin-bottom: var(--spacing-lg);
  color: var(--color-text);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
}

.tools-cards-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.middle-section {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: var(--spacing-xxl);
  overflow-y: auto;
  background: var(--color-white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  border: var(--border-standard);
}

.sessions-section {
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  padding: var(--spacing-xxl);
  overflow-y: auto;
  gap: var(--spacing-xl);
  background: var(--color-white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  border: var(--border-standard);
}

.finished-sessions, .running-sessions {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: var(--spacing-xxl);
  background: var(--gradient-card);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  overflow-y: auto;
  transition: var(--transition-normal);
  border: var(--border-standard);
}

.finished-sessions:hover, .running-sessions:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

/* Typography Styles */
h1, h2, h3, h4, h5, h6 {
  color: var(--color-text);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-lg);
  line-height: 1.2;
}

h1 {
  font-size: var(--font-size-4xl);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

h2 {
  font-size: var(--font-size-3xl);
  color: var(--color-text);
}

h3 {
  font-size: var(--font-size-2xl);
  color: var(--color-text-secondary);
}

/* AutoSpace Section */
.autospace-section {
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xxl);
  margin-bottom: var(--spacing-xl);
}

.default-view {
  color: var(--color-text-light);
  font-size: var(--font-size-lg);
  text-align: center;
  padding: var(--spacing-4xl) var(--spacing-xl);
  background: var(--color-bg-secondary);
  border-radius: var(--border-radius-lg);
  border: var(--border-standard);
  box-shadow: var(--shadow-sm);
}

.selection-view {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
  background: var(--color-white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xxl);
  border: var(--border-standard);
  box-shadow: var(--shadow-md);
}

.tool-name {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin-bottom: var(--spacing-md);
}

.tool-description {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  line-height: 1.6;
  margin-bottom: var(--spacing-xl);
  font-weight: var(--font-weight-medium);
}

.tool-buttons {
  display: flex;
  flex-direction: row;
  gap: var(--spacing-lg);
  align-items: flex-start;
}

/* Button Styles */
.run-button, .download-sample-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-white);
  background: var(--gradient-primary);
  border: none;
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: var(--transition-normal);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: var(--shadow-md);
}

.run-button:hover, .download-sample-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  color: var(--color-text-white);
}

.run-button:active, .download-sample-button:active {
  transform: translateY(0);
  box-shadow: var(--shadow-md);
}

/* File Input Styles */
.file-label {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  background: var(--color-white);
  border: var(--border-standard);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: var(--transition-normal);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: var(--shadow-sm);
}

.file-label:hover {
  background: var(--color-bg-secondary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

#fileInput {
  display: none;
}

/* Checkbox and Label Styles */
input[type="checkbox"] {
  appearance: none;
  width: 18px;
  height: 18px;
  background: var(--color-white);
  border: var(--border-standard);
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  position: relative;
  transition: var(--transition-normal);
  margin-right: var(--spacing-sm);
}

input[type="checkbox"]:checked {
  background: var(--color-primary);
  border-color: var(--color-primary);
}

input[type="checkbox"]:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--color-text-white);
  font-weight: bold;
  font-size: 12px;
}

label[for="mode"] {
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition-fast);
  font-size: var(--font-size-md);
}

label[for="mode"]:hover {
  color: var(--color-text);
}

/* Right Section Styles */
.right-section {
  display: flex;
  flex-direction: column;
  width: 350px;
  gap: var(--spacing-xl);
  flex-grow: .5;
}

.system-performance-section, .active-users-section {
  padding: var(--spacing-xxl);
  background: var(--color-white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  border: var(--border-standard);
}

.box-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin-bottom: var(--spacing-xl);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.box-title i {
  color: var(--color-primary);
  font-size: var(--font-size-2xl);
}

.performance-box, .users-box {
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
}

.performance-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
}

.metric-card {
  background: var(--gradient-card);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  transition: var(--transition-normal);
  box-shadow: var(--shadow-sm);
  border: var(--border-standard);
}

.metric-card:hover {
  background: var(--color-bg-tertiary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.metric-icon i {
  color: var(--color-primary);
  font-size: var(--font-size-2xl);
}

.metric-content {
  display: flex;
  flex-direction: column;
}

.metric-label {
  font-size: var(--font-size-xs);
  color: var(--color-text-light);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metric-value {
  font-size: var(--font-size-lg);
  color: var(--color-text);
  font-weight: var(--font-weight-bold);
}

/* User Count Badge */
.user-count {
  background: var(--color-primary);
  color: var(--color-text-white);
  padding: 4px 12px;
  border-radius: var(--border-radius-pill);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  margin-left: auto;
}

/* Users Container */
.users-container {
  max-height: 300px;
  overflow-y: auto;
}

.no-users {
  text-align: center;
  padding: var(--spacing-3xl) var(--spacing-xl);
  color: var(--color-text-light);
  font-size: var(--font-size-md);
}

.no-users i {
  font-size: var(--font-size-2xl);
  margin-bottom: var(--spacing-md);
  color: var(--color-primary);
}

.user-card {
  background: var(--color-white);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  transition: var(--transition-normal);
  box-shadow: var(--shadow-sm);
  border: var(--border-standard);
}

.user-card:hover {
  background: var(--color-bg-secondary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.user-card.current-user {
  background: var(--gradient-card);
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md);
}

.user-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}

.user-header i {
  color: var(--color-primary);
  font-size: var(--font-size-lg);
}

.username {
  color: var(--color-text);
  font-weight: var(--font-weight-semibold);
  flex-grow: 1;
  font-size: var(--font-size-md);
}

.session-badge {
  background: var(--color-accent);
  color: var(--color-text-white);
  padding: 2px 8px;
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.user-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-sm);
}

.last-activity {
  color: var(--color-text-light);
  font-style: italic;
}

.activity-type {
  color: var(--color-text-secondary);
  background: var(--color-bg-secondary);
  padding: 2px 8px;
  border-radius: var(--border-radius-sm);
  font-weight: var(--font-weight-medium);
}

/* Tool Cards Styles */
.tool-card {
  background: var(--color-white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
  cursor: pointer;
  transition: var(--transition-normal);
  box-shadow: var(--shadow-md);
  border: var(--border-standard);
}

.tool-card:hover {
  background: var(--color-bg-secondary);
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

.tool-card h3 {
  color: var(--color-text);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-md);
}

.tool-card p {
  color: var(--color-text-secondary);
  font-size: var(--font-size-md);
  line-height: 1.5;
  font-weight: var(--font-weight-normal);
}

/* Session Cards Styles */
.session-card {
  background: var(--color-white);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  transition: var(--transition-normal);
  box-shadow: var(--shadow-sm);
  border: var(--border-standard);
  position: relative;
}

.session-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--color-primary);
  border-radius: var(--border-radius-sm) 0 0 var(--border-radius-sm);
  transition: var(--transition-normal);
}

.session-card:hover {
  background: var(--color-bg-secondary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.session-card:hover::before {
  width: 6px;
  background: var(--color-accent);
}

.session-card .title {
  color: var(--color-text);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-sm);
}

.session-card .time {
  color: var(--color-text-light);
  font-size: var(--font-size-xs);
  background: var(--color-bg-secondary);
  padding: 4px 8px;
  border-radius: var(--border-radius-sm);
  display: inline-block;
  margin-bottom: var(--spacing-sm);
  font-weight: var(--font-weight-medium);
}

.progress-counter {
  color: var(--color-primary);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-md);
  background: var(--color-bg-tertiary);
  padding: 4px 8px;
  border-radius: var(--border-radius-sm);
  display: inline-block;
  margin-bottom: var(--spacing-sm);
}

/* Session Action Buttons */
.refresh-button, .kill-button, .download-button {
  background: var(--color-white);
  color: var(--color-text);
  border: var(--border-standard);
  padding: 6px 12px;
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition-normal);
  margin-right: var(--spacing-sm);
  margin-top: var(--spacing-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.refresh-button:hover {
  background: var(--color-primary);
  color: var(--color-text-white);
  border-color: var(--color-primary);
  transform: translateY(-1px);
}

.kill-button:hover {
  background: var(--color-error);
  color: var(--color-text-white);
  border-color: var(--color-error);
  transform: translateY(-1px);
}

.download-button:hover {
  background: var(--color-success);
  color: var(--color-text-white);
  border-color: var(--color-success);
  transform: translateY(-1px);
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--color-bg-secondary);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--color-primary);
  border-radius: 3px;
  transition: var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-accent);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
  }

  .right-section {
    width: 100%;
    flex-direction: row;
  }

  .system-performance-section, .active-users-section {
    flex: 1;
  }
}

@media (max-width: 768px) {
  .side-bar-container {
    width: 80px;
  }

  .main-content {
    padding: var(--spacing-lg);
  }

  .right-section {
    flex-direction: column;
  }

  .performance-metrics {
    grid-template-columns: 1fr;
  }

  .middle-section, .sessions-section {
    padding: var(--spacing-xl);
  }

  .system-performance-section, .active-users-section {
    padding: var(--spacing-xl);
  }
}
