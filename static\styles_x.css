@import url('https://fonts.googleapis.com/css2?family=Nata+Sans:wght@100..900&display=swap');
:root{
  --icon-active-hover: #aa3540;
  --text-white-primary: #c4c2c5;
  --background-color: #232428;  
}
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}


body{
  font-family: "nata-sans",'montserrat', sans-serif;
  background-color: var(--background-color); /* background color */
  color: #c4c2c5;  
  margin: 0;
  display: flex;
  flex-direction: row;
  min-height: 100vh;
  color: var(--text-white-primary);
  overflow-x: hidden;
  font-weight: 100;
}

.side-bar-container {
  color: #c4c2c5;
  background-color: var(--background-color);
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 70px;
  align-items: center;
  padding: 20px 0;
}

.side-bar-container .logo {
  color: var(--icon-active-hover);
  font-size: 3rem;
  margin-bottom: 20px;
  font-weight: 700;
}

.side-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  justify-self: center;
  gap: 40px;
  flex-grow: 1;
}

.tool-category {
  color: var(--text-white-primary);
  display: flex;
  padding: 12px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  font-size: 40px;
  font-weight: 300;
  transition: var(--transition-smooth);
}
.tool-category:focus, .tool-category:hover {
  outline: none;
  font-weight: 700;
  border-left: 2px solid #aa3540;
}
.main-content {
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  padding: 20px;
  gap: 20px;
}


.sessions-section {
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  padding: 30px;
  overflow-y: auto;
  gap: 20px;
}

.tools-cards-section{
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 10px;
  overflow-y: auto;
  max-width: 300px;
  margin: 0;
  gap: 20px;
}
.tools-cards-content{
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 10px;
  overflow-y: auto;
  margin: 0;
  gap: 20px;
}

.tool-card{
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 10px;
  overflow-y: auto;
  max-height: 90px;
  margin: 0;
  border-left: 4px solid #aa3540;
  transition: all 0.3s ease-in-out;
}
.tool-card:hover{
  transform: scale(1.02);
  transform: translateY(-5px);
  border-left: 4px solid #622930;
}

.box-title{
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-white-primary);
  margin-bottom: 20px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.selection-view{
  display: flex;
  flex-direction: column;
  gap: 20px;
  background: var(--glass-bg-secondary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 15px;
  padding: 25px;
}


.system-performance-section{
  padding: 25px;
}

.performance-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.metric-content{
  font-weight: 700;
  display: flex;
  flex-direction: column;
  
}

.metric-card{
  padding: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  background-color: #242425; 

}

.uptime-card{
  background-color: var(--icon-active-hover);
}