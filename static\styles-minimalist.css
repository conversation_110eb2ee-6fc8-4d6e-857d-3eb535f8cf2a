/* Minimalist Theme - Clean, simple design with subtle shadows and neutral colors
 * Design Concept: Ultra-clean interface with plenty of white space, subtle grays, and minimal visual noise
 * Key Features: Clean typography, subtle shadows, neutral color palette, focus on content
 */

:root {
  /* Minimalist Colors */
  --glass-bg-primary: rgba(255, 255, 255, 0.95);
  --glass-bg-secondary: rgba(248, 250, 252, 0.9);
  --glass-bg-tertiary: rgba(241, 245, 249, 0.95);
  --glass-bg-hover: rgba(226, 232, 240, 0.9);

  /* Neutral Accents */
  --accent-black: #1e293b;
  --accent-blue-primary: #475569;
  --accent-blue-secondary: #64748b;
  --accent-blue-light: #94a3b8;
  --accent-blue-dark: #334155;

  /* Minimalist Text Colors */
  --text-white-primary: #0f172a;
  --text-white-secondary: #334155;
  --text-white-tertiary: #64748b;
  --text-white-muted: #94a3b8;

  /* Minimalist Effects */
  --glass-blur: blur(0px);
  --glass-blur-light: blur(0px);
  --glass-border: 1px solid rgba(226, 232, 240, 0.8);
  --glass-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  --glass-shadow-hover: 0 4px 12px rgba(0, 0, 0, 0.15);

  /* Transitions */
  --transition-smooth: all 0.2s ease-out;
  --transition-fast: all 0.15s ease;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', 'system-ui', sans-serif;
  background: linear-gradient(to bottom, #f8fafc 0%, #ffffff 100%);
  background-attachment: fixed;
  margin: 0;
  display: flex;
  flex-direction: row;
  min-height: 100vh;
  color: var(--text-white-primary);
  overflow-x: hidden;
  line-height: 1.6;
}

.side-bar-container {
  display: flex;
  height: 100vh;
  width: 100px;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
}

.side-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 30px;
  padding: 20px;
  width: 60px;
  height: 50vh;
  border-radius: 12px;
  background: var(--glass-bg-primary);
  box-shadow: var(--glass-shadow);
  transition: var(--transition-smooth);
  border: var(--glass-border);
}

.side-bar:hover {
  background: var(--glass-bg-hover);
  box-shadow: var(--glass-shadow-hover);
  transform: translateY(-1px);
}

.tool-category {
  color: var(--text-white-secondary);
  display: flex;
  padding: 10px;
  border-radius: 8px;
  cursor: pointer;
  transition: var(--transition-smooth);
  background: var(--glass-bg-secondary);
  position: relative;
  overflow: hidden;
  border: 1px solid transparent;
}

.tool-category:hover {
  background: var(--glass-bg-tertiary);
  color: var(--text-white-primary);
  transform: scale(1.05);
  border-color: var(--accent-blue-light);
}

.tool-category[aria-selected="true"] {
  background: var(--accent-blue-primary);
  color: white;
  box-shadow: var(--glass-shadow);
}

.main-content {
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  padding: 20px;
  gap: 20px;
}

.tools-cards-section {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 10px;
  border-radius: 12px;
  overflow-y: auto;
  max-width: 270px;
}

.tools-cards-section h2 {
  align-self: flex-start;
  margin-bottom: 10px;
}

.tools-cards-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.middle-section {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 30px;
  overflow-y: auto;
}

.sessions-section {
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  padding: 30px;
  overflow-y: auto;
  gap: 20px;
}

.finished-sessions, .running-sessions {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 25px;
  background: var(--glass-bg-primary);
  border-radius: 12px;
  box-shadow: var(--glass-shadow);
  overflow-y: auto;
  transition: var(--transition-smooth);
  border: var(--glass-border);
}

.finished-sessions:hover, .running-sessions:hover {
  background: var(--glass-bg-secondary);
  transform: translateY(-1px);
  box-shadow: var(--glass-shadow-hover);
}

/* Typography Styles */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-white-primary);
  font-weight: 600;
  margin-bottom: 16px;
  letter-spacing: -0.025em;
}

h1 {
  font-size: 2.25rem;
  font-weight: 700;
  color: var(--accent-blue-primary);
}

h2 {
  font-size: 1.875rem;
  color: var(--text-white-primary);
}

h3 {
  font-size: 1.5rem;
  color: var(--text-white-secondary);
}

/* AutoSpace Section */
.autospace-section {
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 20px;
}

.default-view {
  color: var(--text-white-tertiary);
  font-size: 1rem;
  text-align: center;
  padding: 40px 20px;
  background: var(--glass-bg-secondary);
  border-radius: 8px;
  border: var(--glass-border);
}

.selection-view {
  display: flex;
  flex-direction: column;
  gap: 20px;
  background: var(--glass-bg-primary);
  border-radius: 12px;
  padding: 25px;
  border: var(--glass-border);
  box-shadow: var(--glass-shadow);
}

.tool-name {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--text-white-primary);
  margin-bottom: 8px;
  letter-spacing: -0.025em;
}

.tool-description {
  font-size: 1rem;
  color: var(--text-white-secondary);
  line-height: 1.6;
  margin-bottom: 20px;
}

.tool-buttons {
  display: flex;
  flex-direction: row;
  gap: 12px;
  align-items: flex-start;
}

/* Button Styles */
.run-button, .download-sample-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  font-size: 0.875rem;
  font-weight: 500;
  color: white;
  background: var(--accent-blue-primary);
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: var(--transition-smooth);
  box-shadow: var(--glass-shadow);
  text-transform: none;
  letter-spacing: 0;
}

.run-button:hover, .download-sample-button:hover {
  background: var(--accent-blue-dark);
  transform: translateY(-1px);
  box-shadow: var(--glass-shadow-hover);
}

.run-button:active, .download-sample-button:active {
  transform: translateY(0);
  box-shadow: var(--glass-shadow);
}

/* File Input Styles */
.file-label {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--accent-blue-primary);
  background: var(--glass-bg-secondary);
  border: 1px solid var(--accent-blue-primary);
  border-radius: 6px;
  cursor: pointer;
  transition: var(--transition-smooth);
  text-transform: none;
  letter-spacing: 0;
}

.file-label:hover {
  background: var(--accent-blue-primary);
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--glass-shadow);
}

#fileInput {
  display: none;
}

/* Checkbox and Label Styles */
input[type="checkbox"] {
  appearance: none;
  width: 18px;
  height: 18px;
  background: var(--glass-bg-secondary);
  border: 2px solid var(--accent-blue-light);
  border-radius: 3px;
  cursor: pointer;
  position: relative;
  transition: var(--transition-smooth);
  margin-right: 8px;
}

input[type="checkbox"]:checked {
  background: var(--accent-blue-primary);
  border-color: var(--accent-blue-primary);
}

input[type="checkbox"]:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-weight: bold;
  font-size: 12px;
}

label[for="mode"] {
  color: var(--text-white-secondary);
  font-weight: 400;
  cursor: pointer;
  transition: var(--transition-fast);
}

label[for="mode"]:hover {
  color: var(--text-white-primary);
}

/* Right Section Styles */
.right-section {
  display: flex;
  flex-direction: column;
  width: 350px;
  gap: 20px;
  flex-grow: .5;
}

.system-performance-section, .active-users-section {
  padding: 25px;
}

.box-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-white-primary);
  margin-bottom: 16px;
  text-transform: none;
  letter-spacing: -0.025em;
}

.box-title i {
  color: var(--accent-blue-primary);
  font-size: 1.25rem;
}

.performance-box, .users-box {
  border-radius: 12px;
  padding: 20px;
}

.performance-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.metric-card {
  background: var(--glass-bg-secondary);
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: var(--transition-smooth);
  box-shadow: var(--glass-shadow);
  border: var(--glass-border);
}

.metric-card:hover {
  background: var(--glass-bg-tertiary);
  transform: translateY(-1px);
  box-shadow: var(--glass-shadow-hover);
}

.metric-icon i {
  color: var(--accent-blue-primary);
  font-size: 1.5rem;
}

.metric-content {
  display: flex;
  flex-direction: column;
}

.metric-label {
  font-size: 0.75rem;
  color: var(--text-white-tertiary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.metric-value {
  font-size: 1rem;
  color: var(--text-white-primary);
  font-weight: 600;
}

/* User Count Badge */
.user-count {
  background: var(--accent-blue-primary);
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 500;
  margin-left: auto;
}

/* Users Container */
.users-container {
  max-height: 300px;
  overflow-y: auto;
}

.no-users {
  text-align: center;
  padding: 30px 20px;
  color: var(--text-white-tertiary);
}

.no-users i {
  font-size: 1.5rem;
  margin-bottom: 8px;
  color: var(--accent-blue-primary);
}

.user-card {
  background: var(--glass-bg-secondary);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 8px;
  transition: var(--transition-smooth);
  box-shadow: var(--glass-shadow);
  border: var(--glass-border);
}

.user-card:hover {
  background: var(--glass-bg-tertiary);
  transform: translateX(2px);
  box-shadow: var(--glass-shadow-hover);
}

.user-card.current-user {
  background: var(--accent-blue-primary);
  color: white;
  box-shadow: var(--glass-shadow-hover);
}

.user-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.user-header i {
  color: var(--accent-blue-primary);
  font-size: 1rem;
}

.user-card.current-user .user-header i {
  color: white;
}

.username {
  color: var(--text-white-primary);
  font-weight: 500;
  flex-grow: 1;
}

.user-card.current-user .username {
  color: white;
}

.session-badge {
  background: var(--accent-blue-secondary);
  color: white;
  padding: 2px 8px;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 500;
}

.user-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
}

.last-activity {
  color: var(--text-white-tertiary);
}

.user-card.current-user .last-activity {
  color: rgba(255, 255, 255, 0.8);
}

.activity-type {
  color: var(--text-white-secondary);
  background: var(--glass-bg-primary);
  padding: 2px 8px;
  border-radius: 6px;
  font-weight: 400;
}

/* Tool Cards Styles */
.tool-card {
  background: var(--glass-bg-primary);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: var(--transition-smooth);
  box-shadow: var(--glass-shadow);
  border: var(--glass-border);
}

.tool-card:hover {
  background: var(--glass-bg-secondary);
  transform: translateY(-2px);
  box-shadow: var(--glass-shadow-hover);
}

.tool-card h3 {
  color: var(--text-white-primary);
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 8px;
  letter-spacing: -0.025em;
}

.tool-card p {
  color: var(--text-white-secondary);
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Session Cards Styles */
.session-card {
  background: var(--glass-bg-primary);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 8px;
  transition: var(--transition-smooth);
  box-shadow: var(--glass-shadow);
  border: var(--glass-border);
  position: relative;
}

.session-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background: var(--accent-blue-primary);
  border-radius: 0 0 0 8px;
  transition: var(--transition-smooth);
}

.session-card:hover {
  background: var(--glass-bg-secondary);
  transform: translateX(2px);
  box-shadow: var(--glass-shadow-hover);
}

.session-card:hover::before {
  width: 4px;
  background: var(--accent-blue-dark);
}

.session-card .title {
  color: var(--text-white-primary);
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 8px;
}

.session-card .time {
  color: var(--text-white-tertiary);
  font-size: 0.75rem;
  background: var(--glass-bg-secondary);
  padding: 4px 8px;
  border-radius: 6px;
  display: inline-block;
  margin-bottom: 8px;
}

.progress-counter {
  color: var(--accent-blue-primary);
  font-weight: 500;
  font-size: 0.875rem;
  background: var(--glass-bg-secondary);
  padding: 4px 8px;
  border-radius: 6px;
  display: inline-block;
  margin-bottom: 8px;
}

/* Session Action Buttons */
.refresh-button, .kill-button, .download-button {
  background: var(--glass-bg-secondary);
  color: var(--text-white-primary);
  border: 1px solid var(--accent-blue-light);
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-smooth);
  margin-right: 6px;
  margin-top: 6px;
  text-transform: none;
  letter-spacing: 0;
}

.refresh-button:hover {
  background: var(--accent-blue-primary);
  color: white;
  border-color: var(--accent-blue-primary);
  transform: translateY(-1px);
}

.kill-button:hover {
  background: #dc2626;
  color: white;
  border-color: #dc2626;
  transform: translateY(-1px);
}

.download-button:hover {
  background: #059669;
  color: white;
  border-color: #059669;
  transform: translateY(-1px);
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--glass-bg-secondary);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--accent-blue-light);
  border-radius: 3px;
  transition: var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-blue-primary);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
  }

  .right-section {
    width: 100%;
    flex-direction: row;
  }

  .system-performance-section, .active-users-section {
    flex: 1;
  }
}

@media (max-width: 768px) {
  .side-bar-container {
    width: 80px;
  }

  .main-content {
    padding: 15px;
  }

  .right-section {
    flex-direction: column;
  }

  .performance-metrics {
    grid-template-columns: 1fr;
  }
}
