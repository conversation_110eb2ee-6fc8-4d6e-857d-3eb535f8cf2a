/* Retro/Vintage Theme - 80s-inspired design with neon colors and retro aesthetics
 * Design Concept: Cyberpunk/synthwave aesthetic with neon pink, cyan, and dark backgrounds
 * Key Features: Neon glow effects, retro typography, vibrant color contrasts, 80s vibes
 */

:root {
  /* Retro Colors */
  --glass-bg-primary: rgba(20, 0, 40, 0.9);
  --glass-bg-secondary: rgba(40, 0, 60, 0.8);
  --glass-bg-tertiary: rgba(60, 0, 80, 0.85);
  --glass-bg-hover: rgba(80, 0, 100, 0.9);

  /* Neon Accents */
  --accent-black: #0a0a0a;
  --accent-blue-primary: #ff0080;
  --accent-blue-secondary: #00ffff;
  --accent-blue-light: #ff00ff;
  --accent-blue-dark: #8000ff;

  /* Retro Text Colors */
  --text-white-primary: rgba(255, 255, 255, 0.95);
  --text-white-secondary: rgba(0, 255, 255, 0.9);
  --text-white-tertiary: rgba(255, 0, 255, 0.8);
  --text-white-muted: rgba(255, 255, 255, 0.6);

  /* Retro Effects */
  --glass-blur: blur(10px);
  --glass-blur-light: blur(5px);
  --glass-border: 2px solid rgba(255, 0, 255, 0.5);
  --glass-shadow: 0 0 20px rgba(255, 0, 255, 0.3);
  --glass-shadow-hover: 0 0 30px rgba(255, 0, 255, 0.5);

  /* Transitions */
  --transition-smooth: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --transition-fast: all 0.2s ease;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Orbitron', 'Courier New', monospace;
  background: linear-gradient(45deg, #000000 0%, #1a0033 25%, #330066 50%, #1a0033 75%, #000000 100%);
  background-size: 400% 400%;
  animation: retroGradient 8s ease infinite;
  background-attachment: fixed;
  margin: 0;
  display: flex;
  flex-direction: row;
  min-height: 100vh;
  color: var(--text-white-primary);
  overflow-x: hidden;
}

@keyframes retroGradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.side-bar-container {
  display: flex;
  height: 100vh;
  width: 100px;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
}

.side-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 40px;
  padding: 25px;
  width: 60px;
  height: 50vh;
  border-radius: 0;
  background: var(--glass-bg-primary);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  box-shadow: var(--glass-shadow);
  transition: var(--transition-smooth);
  border: var(--glass-border);
  position: relative;
}

.side-bar::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, var(--accent-blue-primary), var(--accent-blue-secondary), var(--accent-blue-light));
  z-index: -1;
  border-radius: 0;
  animation: neonBorder 2s linear infinite;
}

@keyframes neonBorder {
  0% { filter: hue-rotate(0deg); }
  100% { filter: hue-rotate(360deg); }
}

.side-bar:hover {
  background: var(--glass-bg-hover);
  box-shadow: var(--glass-shadow-hover);
  transform: translateY(-2px) scale(1.02);
}

.tool-category {
  color: var(--text-white-primary);
  display: flex;
  padding: 12px;
  border-radius: 0;
  cursor: pointer;
  transition: var(--transition-smooth);
  background: var(--glass-bg-secondary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  position: relative;
  overflow: hidden;
  border: 1px solid var(--accent-blue-secondary);
  text-shadow: 0 0 10px currentColor;
}

.tool-category::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, var(--accent-blue-primary), var(--accent-blue-secondary));
  opacity: 0;
  transition: var(--transition-smooth);
  z-index: -1;
}

.tool-category:hover {
  background: var(--glass-bg-tertiary);
  color: var(--accent-blue-secondary);
  transform: scale(1.1);
  box-shadow: 0 0 20px var(--accent-blue-secondary);
  text-shadow: 0 0 15px currentColor;
}

.tool-category:hover::before {
  opacity: 0.3;
}

.tool-category[aria-selected="true"] {
  background: var(--accent-blue-primary);
  color: var(--text-white-primary);
  box-shadow: 0 0 25px var(--accent-blue-primary);
  text-shadow: 0 0 15px currentColor;
}

.main-content {
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  padding: 20px;
  gap: 20px;
}

.tools-cards-section {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 10px;
  border-radius: 0;
  overflow-y: auto;
  max-width: 270px;
}

.tools-cards-section h2 {
  align-self: flex-start;
  margin-bottom: 10px;
}

.tools-cards-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.middle-section {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 30px;
  overflow-y: auto;
}

.sessions-section {
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  padding: 30px;
  overflow-y: auto;
  gap: 20px;
}

.finished-sessions, .running-sessions {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 25px;
  background: var(--glass-bg-secondary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 0;
  box-shadow: var(--glass-shadow);
  overflow-y: auto;
  transition: var(--transition-smooth);
  border: var(--glass-border);
}

.finished-sessions:hover, .running-sessions:hover {
  background: var(--glass-bg-tertiary);
  transform: translateY(-2px);
  box-shadow: var(--glass-shadow-hover);
}

/* Typography Styles */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-white-primary);
  font-weight: 700;
  margin-bottom: 20px;
  text-shadow: 0 0 15px currentColor;
  text-transform: uppercase;
  letter-spacing: 2px;
}

h1 {
  font-size: 2.5rem;
  background: linear-gradient(45deg, var(--accent-blue-primary), var(--accent-blue-secondary), var(--accent-blue-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: neonText 2s ease-in-out infinite alternate;
}

@keyframes neonText {
  from { filter: drop-shadow(0 0 10px var(--accent-blue-primary)); }
  to { filter: drop-shadow(0 0 20px var(--accent-blue-secondary)); }
}

h2 {
  font-size: 2rem;
  color: var(--accent-blue-secondary);
}

h3 {
  font-size: 1.5rem;
  color: var(--text-white-secondary);
}

/* AutoSpace Section */
.autospace-section {
  border-radius: 0;
  padding: 25px;
  margin-bottom: 20px;
}

.default-view {
  color: var(--text-white-tertiary);
  font-size: 1.1rem;
  text-align: center;
  padding: 40px 20px;
  background: var(--glass-bg-secondary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 0;
  font-style: italic;
  border: var(--glass-border);
  text-shadow: 0 0 10px currentColor;
}

.selection-view {
  display: flex;
  flex-direction: column;
  gap: 20px;
  background: var(--glass-bg-secondary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 0;
  padding: 25px;
  border: var(--glass-border);
  box-shadow: var(--glass-shadow);
}

.tool-name {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--accent-blue-primary);
  margin-bottom: 10px;
  text-shadow: 0 0 15px currentColor;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.tool-description {
  font-size: 1.1rem;
  color: var(--text-white-secondary);
  line-height: 1.6;
  margin-bottom: 20px;
  text-shadow: 0 0 5px currentColor;
}

.tool-buttons {
  display: flex;
  flex-direction: row;
  gap: 15px;
  align-items: flex-start;
}

/* Button Styles */
.run-button, .download-sample-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 15px 30px;
  font-size: 1rem;
  font-weight: 700;
  color: var(--text-white-primary);
  background: var(--glass-bg-primary);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 2px solid var(--accent-blue-primary);
  border-radius: 0;
  cursor: pointer;
  transition: var(--transition-smooth);
  text-transform: uppercase;
  letter-spacing: 2px;
  box-shadow: var(--glass-shadow);
  position: relative;
  overflow: hidden;
  text-shadow: 0 0 10px currentColor;
}

.run-button::before, .download-sample-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, var(--accent-blue-primary), var(--accent-blue-secondary));
  opacity: 0;
  transition: var(--transition-smooth);
  z-index: -1;
}

.run-button:hover, .download-sample-button:hover {
  background: var(--glass-bg-hover);
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 0 30px var(--accent-blue-primary);
  color: var(--accent-blue-secondary);
  border-color: var(--accent-blue-secondary);
  text-shadow: 0 0 15px currentColor;
}

.run-button:hover::before, .download-sample-button:hover::before {
  opacity: 0.3;
}

.run-button:active, .download-sample-button:active {
  transform: translateY(-1px) scale(1.02);
}

/* File Input Styles */
.file-label {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 15px 30px;
  font-size: 1rem;
  font-weight: 700;
  color: var(--text-white-primary);
  background: var(--glass-bg-primary);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border-radius: 0;
  cursor: pointer;
  transition: var(--transition-smooth);
  text-transform: uppercase;
  letter-spacing: 2px;
  box-shadow: var(--glass-shadow);
  position: relative;
  overflow: hidden;
  border: 2px solid var(--accent-blue-secondary);
  text-shadow: 0 0 10px currentColor;
}

.file-label::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, var(--accent-blue-secondary), var(--accent-blue-light));
  opacity: 0;
  transition: var(--transition-smooth);
  z-index: -1;
}

.file-label:hover {
  background: var(--glass-bg-hover);
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 0 30px var(--accent-blue-secondary);
  color: var(--accent-blue-light);
  text-shadow: 0 0 15px currentColor;
}

.file-label:hover::before {
  opacity: 0.3;
}

#fileInput {
  display: none;
}

/* Checkbox and Label Styles */
input[type="checkbox"] {
  appearance: none;
  width: 20px;
  height: 20px;
  background: var(--glass-bg-secondary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 0;
  cursor: pointer;
  position: relative;
  transition: var(--transition-smooth);
  margin-right: 10px;
  border: 2px solid var(--accent-blue-secondary);
  box-shadow: 0 0 10px var(--accent-blue-secondary);
}

input[type="checkbox"]:checked {
  background: var(--accent-blue-primary);
  box-shadow: 0 0 20px var(--accent-blue-primary);
  border-color: var(--accent-blue-primary);
}

input[type="checkbox"]:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--text-white-primary);
  font-weight: bold;
  font-size: 14px;
  text-shadow: 0 0 10px currentColor;
}

label[for="mode"] {
  color: var(--text-white-secondary);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-fast);
  text-shadow: 0 0 5px currentColor;
  text-transform: uppercase;
  letter-spacing: 1px;
}

label[for="mode"]:hover {
  color: var(--accent-blue-light);
  text-shadow: 0 0 10px currentColor;
}

/* Right Section Styles */
.right-section {
  display: flex;
  flex-direction: column;
  width: 350px;
  gap: 20px;
  flex-grow: .5;
}

.system-performance-section, .active-users-section {
  padding: 25px;
}

.box-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--accent-blue-secondary);
  margin-bottom: 20px;
  text-transform: uppercase;
  letter-spacing: 2px;
  text-shadow: 0 0 15px currentColor;
}

.box-title i {
  color: var(--accent-blue-light);
  font-size: 1.5rem;
  text-shadow: 0 0 15px currentColor;
}

.performance-box, .users-box {
  border-radius: 0;
  padding: 20px;
}

.performance-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.metric-card {
  background: var(--glass-bg-tertiary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 0;
  padding: 15px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: var(--transition-smooth);
  box-shadow: 0 0 15px rgba(255, 0, 255, 0.2);
  border: 1px solid var(--accent-blue-light);
}

.metric-card:hover {
  background: var(--glass-bg-hover);
  transform: translateY(-2px);
  box-shadow: 0 0 25px rgba(255, 0, 255, 0.4);
}

.metric-icon i {
  color: var(--accent-blue-light);
  font-size: 1.8rem;
  text-shadow: 0 0 15px currentColor;
}

.metric-content {
  display: flex;
  flex-direction: column;
}

.metric-label {
  font-size: 0.85rem;
  color: var(--text-white-tertiary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: 0 0 5px currentColor;
}

.metric-value {
  font-size: 1.1rem;
  color: var(--text-white-primary);
  font-weight: 700;
  text-shadow: 0 0 10px currentColor;
}

/* User Count Badge */
.user-count {
  background: var(--accent-blue-primary);
  color: var(--text-white-primary);
  padding: 4px 12px;
  border-radius: 0;
  font-size: 0.9rem;
  font-weight: 700;
  margin-left: auto;
  text-shadow: 0 0 10px currentColor;
  box-shadow: 0 0 15px var(--accent-blue-primary);
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Users Container */
.users-container {
  max-height: 300px;
  overflow-y: auto;
}

.no-users {
  text-align: center;
  padding: 30px 20px;
  color: var(--text-white-tertiary);
  text-shadow: 0 0 10px currentColor;
}

.no-users i {
  font-size: 2rem;
  margin-bottom: 10px;
  color: var(--accent-blue-light);
  text-shadow: 0 0 15px currentColor;
}

.user-card {
  background: var(--glass-bg-tertiary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 0;
  padding: 15px;
  margin-bottom: 10px;
  transition: var(--transition-smooth);
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.2);
  border: 1px solid var(--accent-blue-secondary);
}

.user-card:hover {
  background: var(--glass-bg-hover);
  transform: translateX(5px);
  box-shadow: 0 0 25px rgba(0, 255, 255, 0.4);
}

.user-card.current-user {
  background: linear-gradient(135deg, var(--glass-bg-tertiary), var(--accent-blue-primary));
  box-shadow: 0 0 25px var(--accent-blue-primary);
  border-color: var(--accent-blue-primary);
}

.user-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.user-header i {
  color: var(--accent-blue-light);
  font-size: 1.2rem;
  text-shadow: 0 0 10px currentColor;
}

.username {
  color: var(--text-white-primary);
  font-weight: 700;
  flex-grow: 1;
  text-shadow: 0 0 10px currentColor;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.session-badge {
  background: var(--accent-blue-secondary);
  color: var(--text-white-primary);
  padding: 2px 8px;
  border-radius: 0;
  font-size: 0.75rem;
  font-weight: 700;
  text-shadow: 0 0 5px currentColor;
  box-shadow: 0 0 10px var(--accent-blue-secondary);
  text-transform: uppercase;
}

.user-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.85rem;
}

.last-activity {
  color: var(--text-white-tertiary);
  font-style: italic;
  text-shadow: 0 0 5px currentColor;
}

.activity-type {
  color: var(--text-white-secondary);
  background: var(--glass-bg-secondary);
  padding: 2px 8px;
  border-radius: 0;
  font-weight: 500;
  text-shadow: 0 0 5px currentColor;
  border: 1px solid var(--accent-blue-light);
  text-transform: uppercase;
}

/* Tool Cards Styles */
.tool-card {
  background: var(--glass-bg-secondary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 0;
  padding: 20px;
  margin-bottom: 15px;
  cursor: pointer;
  transition: var(--transition-smooth);
  box-shadow: 0 0 20px rgba(255, 0, 255, 0.2);
  position: relative;
  overflow: hidden;
  border: 2px solid var(--accent-blue-light);
}

.tool-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--accent-blue-primary), var(--accent-blue-secondary));
  opacity: 0;
  transition: var(--transition-smooth);
  z-index: -1;
}

.tool-card:hover {
  background: var(--glass-bg-tertiary);
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 0 30px rgba(255, 0, 255, 0.4);
}

.tool-card:hover::before {
  opacity: 0.2;
}

.tool-card h3 {
  color: var(--accent-blue-primary);
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 10px;
  text-shadow: 0 0 15px currentColor;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.tool-card p {
  color: var(--text-white-secondary);
  font-size: 0.95rem;
  line-height: 1.5;
  text-shadow: 0 0 5px currentColor;
}

/* Session Cards Styles */
.session-card {
  background: var(--glass-bg-secondary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 0;
  padding: 18px;
  margin-bottom: 12px;
  transition: var(--transition-smooth);
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  border: 1px solid var(--accent-blue-secondary);
}

.session-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--accent-blue-secondary);
  transition: var(--transition-smooth);
  box-shadow: 0 0 10px var(--accent-blue-secondary);
}

.session-card:hover {
  background: var(--glass-bg-tertiary);
  transform: translateX(5px);
  box-shadow: 0 0 30px rgba(0, 255, 255, 0.4);
}

.session-card:hover::before {
  width: 6px;
  background: var(--accent-blue-light);
  box-shadow: 0 0 15px var(--accent-blue-light);
}

.session-card .title {
  color: var(--text-white-primary);
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 10px;
  text-shadow: 0 0 10px currentColor;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.session-card .time {
  color: var(--text-white-tertiary);
  font-size: 0.85rem;
  background: var(--glass-bg-primary);
  padding: 4px 10px;
  border-radius: 0;
  display: inline-block;
  margin-bottom: 10px;
  text-shadow: 0 0 5px currentColor;
  border: 1px solid var(--accent-blue-light);
}

.progress-counter {
  color: var(--accent-blue-light);
  font-weight: 700;
  font-size: 0.9rem;
  background: var(--glass-bg-primary);
  padding: 4px 10px;
  border-radius: 0;
  display: inline-block;
  margin-bottom: 10px;
  text-shadow: 0 0 10px currentColor;
  border: 1px solid var(--accent-blue-light);
  text-transform: uppercase;
}

/* Session Action Buttons */
.refresh-button, .kill-button, .download-button {
  background: var(--glass-bg-primary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  color: var(--text-white-primary);
  border: 2px solid var(--accent-blue-secondary);
  padding: 8px 16px;
  border-radius: 0;
  font-size: 0.85rem;
  font-weight: 700;
  cursor: pointer;
  transition: var(--transition-smooth);
  margin-right: 8px;
  margin-top: 8px;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: 0 0 10px currentColor;
}

.refresh-button:hover {
  background: var(--accent-blue-secondary);
  transform: translateY(-2px);
  box-shadow: 0 0 20px var(--accent-blue-secondary);
  color: var(--text-white-primary);
}

.kill-button:hover {
  background: #ff0040;
  border-color: #ff0040;
  transform: translateY(-2px);
  box-shadow: 0 0 20px #ff0040;
  color: var(--text-white-primary);
}

.download-button:hover {
  background: #00ff80;
  border-color: #00ff80;
  transform: translateY(-2px);
  box-shadow: 0 0 20px #00ff80;
  color: var(--accent-black);
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--glass-bg-secondary);
  border-radius: 0;
}

::-webkit-scrollbar-thumb {
  background: var(--accent-blue-secondary);
  border-radius: 0;
  transition: var(--transition-fast);
  box-shadow: 0 0 10px var(--accent-blue-secondary);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-blue-light);
  box-shadow: 0 0 15px var(--accent-blue-light);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
  }

  .right-section {
    width: 100%;
    flex-direction: row;
  }

  .system-performance-section, .active-users-section {
    flex: 1;
  }
}

@media (max-width: 768px) {
  .side-bar-container {
    width: 80px;
  }

  .main-content {
    padding: 15px;
  }

  .right-section {
    flex-direction: column;
  }

  .performance-metrics {
    grid-template-columns: 1fr;
  }
}
