/* Nature-Inspired Theme - Organic design with earth tones and natural textures
 * Design Concept: Earthy, organic interface inspired by nature with greens, browns, and natural textures
 * Key Features: Earth tone color palette, organic shapes, nature-inspired animations, warm textures
 */

:root {
  /* Nature Colors */
  --glass-bg-primary: rgba(76, 110, 82, 0.15);
  --glass-bg-secondary: rgba(101, 143, 112, 0.1);
  --glass-bg-tertiary: rgba(139, 195, 74, 0.2);
  --glass-bg-hover: rgba(165, 214, 167, 0.25);

  /* Earth Tone Accents */
  --accent-black: #2e2e2e;
  --accent-blue-primary: #4caf50;
  --accent-blue-secondary: #66bb6a;
  --accent-blue-light: #81c784;
  --accent-blue-dark: #388e3c;

  /* Nature Text Colors */
  --text-white-primary: rgba(46, 46, 46, 0.95);
  --text-white-secondary: rgba(76, 110, 82, 0.9);
  --text-white-tertiary: rgba(101, 143, 112, 0.8);
  --text-white-muted: rgba(139, 195, 74, 0.7);

  /* Nature Effects */
  --glass-blur: blur(15px);
  --glass-blur-light: blur(8px);
  --glass-border: 2px solid rgba(139, 195, 74, 0.3);
  --glass-shadow: 0 8px 32px rgba(76, 110, 82, 0.2);
  --glass-shadow-hover: 0 12px 40px rgba(76, 110, 82, 0.3);

  /* Transitions */
  --transition-smooth: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --transition-fast: all 0.2s ease;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Nunito', 'Georgia', serif;
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 25%, #a5d6a7 50%, #81c784 75%, #66bb6a 100%);
  background-size: 400% 400%;
  animation: natureBreeze 20s ease infinite;
  background-attachment: fixed;
  margin: 0;
  display: flex;
  flex-direction: row;
  min-height: 100vh;
  color: var(--text-white-primary);
  overflow-x: hidden;
  position: relative;
}

@keyframes natureBreeze {
  0%, 100% { background-position: 0% 50%; }
  25% { background-position: 100% 25%; }
  50% { background-position: 50% 100%; }
  75% { background-position: 25% 0%; }
}

body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(139, 195, 74, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(76, 110, 82, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(165, 214, 167, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

.side-bar-container {
  display: flex;
  height: 100vh;
  width: 100px;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
}

.side-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 40px;
  padding: 25px;
  width: 60px;
  height: 50vh;
  border-radius: 30px;
  background: var(--glass-bg-primary);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  box-shadow: var(--glass-shadow);
  transition: var(--transition-smooth);
  border: var(--glass-border);
  position: relative;
}

.side-bar::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, var(--accent-blue-primary), var(--accent-blue-secondary), var(--accent-blue-light));
  border-radius: 32px;
  z-index: -1;
  opacity: 0;
  transition: var(--transition-smooth);
}

.side-bar:hover {
  background: var(--glass-bg-hover);
  box-shadow: var(--glass-shadow-hover);
  transform: translateY(-2px) scale(1.02);
}

.side-bar:hover::before {
  opacity: 0.3;
}

.tool-category {
  color: var(--text-white-secondary);
  display: flex;
  padding: 12px;
  border-radius: 50%;
  cursor: pointer;
  transition: var(--transition-smooth);
  background: var(--glass-bg-secondary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  position: relative;
  overflow: hidden;
  border: 2px solid transparent;
}

.tool-category::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle, var(--accent-blue-primary), var(--accent-blue-secondary));
  opacity: 0;
  transition: var(--transition-smooth);
  z-index: -1;
  border-radius: 50%;
}

.tool-category:hover {
  background: var(--glass-bg-tertiary);
  color: var(--text-white-primary);
  transform: scale(1.1) rotate(10deg);
  box-shadow: 0 8px 25px rgba(76, 110, 82, 0.4);
  border-color: var(--accent-blue-light);
}

.tool-category:hover::before {
  opacity: 0.2;
}

.tool-category[aria-selected="true"] {
  background: var(--accent-blue-primary);
  color: white;
  box-shadow: 0 8px 25px rgba(76, 110, 82, 0.6);
  border-color: var(--accent-blue-dark);
}

.main-content {
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  padding: 20px;
  gap: 20px;
}

.tools-cards-section {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 10px;
  border-radius: 25px;
  overflow-y: auto;
  max-width: 270px;
}

.tools-cards-section h2 {
  align-self: flex-start;
  margin-bottom: 10px;
}

.tools-cards-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.middle-section {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 30px;
  overflow-y: auto;
}

.sessions-section {
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  padding: 30px;
  overflow-y: auto;
  gap: 20px;
}

.finished-sessions, .running-sessions {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 25px;
  background: var(--glass-bg-secondary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 25px;
  box-shadow: var(--glass-shadow);
  overflow-y: auto;
  transition: var(--transition-smooth);
  border: var(--glass-border);
  position: relative;
}

.finished-sessions::before, .running-sessions::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at top, rgba(139, 195, 74, 0.1), transparent 70%);
  border-radius: 25px;
  pointer-events: none;
}

.finished-sessions:hover, .running-sessions:hover {
  background: var(--glass-bg-tertiary);
  transform: translateY(-3px) scale(1.01);
  box-shadow: var(--glass-shadow-hover);
}

/* Typography Styles */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-white-primary);
  font-weight: 700;
  margin-bottom: 20px;
  text-shadow: 0 2px 10px rgba(76, 110, 82, 0.2);
}

h1 {
  font-size: 2.5rem;
  background: linear-gradient(135deg, var(--accent-blue-dark), var(--accent-blue-primary), var(--accent-blue-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

h1::after {
  content: '🌿';
  position: absolute;
  right: -40px;
  top: 0;
  font-size: 0.8em;
  animation: leafSway 3s ease-in-out infinite;
}

@keyframes leafSway {
  0%, 100% { transform: rotate(-5deg); }
  50% { transform: rotate(5deg); }
}

h2 {
  font-size: 2rem;
  color: var(--accent-blue-primary);
}

h3 {
  font-size: 1.5rem;
  color: var(--text-white-secondary);
}

/* AutoSpace Section */
.autospace-section {
  border-radius: 20px;
  padding: 25px;
  margin-bottom: 20px;
}

.default-view {
  color: var(--text-white-tertiary);
  font-size: 1.1rem;
  text-align: center;
  padding: 40px 20px;
  background: var(--glass-bg-secondary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 20px;
  font-style: italic;
  border: var(--glass-border);
  position: relative;
}

.default-view::before {
  content: '🍃';
  position: absolute;
  top: 15px;
  right: 20px;
  font-size: 1.5rem;
  animation: leafFloat 4s ease-in-out infinite;
}

@keyframes leafFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(10deg); }
}

.selection-view {
  display: flex;
  flex-direction: column;
  gap: 20px;
  background: var(--glass-bg-secondary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 20px;
  padding: 25px;
  border: var(--glass-border);
  position: relative;
}

.selection-view::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 80% 20%, rgba(139, 195, 74, 0.1), transparent 50%);
  border-radius: 20px;
  pointer-events: none;
}

.tool-name {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--accent-blue-primary);
  margin-bottom: 10px;
  text-shadow: 0 2px 10px rgba(76, 110, 82, 0.2);
  position: relative;
}

.tool-name::after {
  content: '🌱';
  position: absolute;
  right: -30px;
  top: 0;
  font-size: 0.7em;
}

.tool-description {
  font-size: 1.1rem;
  color: var(--text-white-secondary);
  line-height: 1.6;
  margin-bottom: 20px;
}

.tool-buttons {
  display: flex;
  flex-direction: row;
  gap: 15px;
  align-items: flex-start;
}

/* Button Styles */
.run-button, .download-sample-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 15px 30px;
  font-size: 1rem;
  font-weight: 600;
  color: white;
  background: linear-gradient(135deg, var(--accent-blue-primary), var(--accent-blue-secondary));
  border: none;
  border-radius: 25px;
  cursor: pointer;
  transition: var(--transition-smooth);
  text-transform: capitalize;
  letter-spacing: 0.5px;
  box-shadow: var(--glass-shadow);
  position: relative;
  overflow: hidden;
}

.run-button::before, .download-sample-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--accent-blue-secondary), var(--accent-blue-light));
  opacity: 0;
  transition: var(--transition-smooth);
  z-index: -1;
  border-radius: 25px;
}

.run-button:hover, .download-sample-button:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 15px 35px rgba(76, 110, 82, 0.4);
  color: white;
}

.run-button:hover::before, .download-sample-button:hover::before {
  opacity: 1;
}

.run-button:active, .download-sample-button:active {
  transform: translateY(-1px) scale(1.02);
}

/* File Input Styles */
.file-label {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 15px 30px;
  font-size: 1rem;
  font-weight: 600;
  color: var(--accent-blue-primary);
  background: var(--glass-bg-primary);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 2px solid var(--accent-blue-primary);
  border-radius: 25px;
  cursor: pointer;
  transition: var(--transition-smooth);
  text-transform: capitalize;
  letter-spacing: 0.5px;
  box-shadow: var(--glass-shadow);
  position: relative;
  overflow: hidden;
}

.file-label::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--accent-blue-primary), var(--accent-blue-secondary));
  opacity: 0;
  transition: var(--transition-smooth);
  z-index: -1;
  border-radius: 25px;
}

.file-label:hover {
  background: var(--glass-bg-hover);
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 15px 35px rgba(76, 110, 82, 0.4);
  color: white;
}

.file-label:hover::before {
  opacity: 1;
}

#fileInput {
  display: none;
}

/* Checkbox and Label Styles */
input[type="checkbox"] {
  appearance: none;
  width: 20px;
  height: 20px;
  background: var(--glass-bg-secondary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  transition: var(--transition-smooth);
  margin-right: 10px;
  border: 2px solid var(--accent-blue-light);
}

input[type="checkbox"]:checked {
  background: var(--accent-blue-primary);
  box-shadow: 0 0 15px rgba(76, 110, 82, 0.5);
  border-color: var(--accent-blue-primary);
}

input[type="checkbox"]:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-weight: bold;
  font-size: 14px;
}

label[for="mode"] {
  color: var(--text-white-secondary);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-fast);
}

label[for="mode"]:hover {
  color: var(--accent-blue-primary);
}

/* Right Section Styles */
.right-section {
  display: flex;
  flex-direction: column;
  width: 350px;
  gap: 20px;
  flex-grow: .5;
}

.system-performance-section, .active-users-section {
  padding: 25px;
}

.box-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--accent-blue-primary);
  margin-bottom: 20px;
  text-transform: capitalize;
  letter-spacing: 0.5px;
  position: relative;
}

.box-title::after {
  content: '🌿';
  margin-left: 5px;
  font-size: 1rem;
}

.box-title i {
  color: var(--accent-blue-light);
  font-size: 1.5rem;
}

.performance-box, .users-box {
  border-radius: 20px;
  padding: 20px;
}

.performance-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.metric-card {
  background: var(--glass-bg-tertiary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 15px;
  padding: 15px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: var(--transition-smooth);
  box-shadow: 0 4px 20px rgba(76, 110, 82, 0.15);
  border: 2px solid rgba(139, 195, 74, 0.2);
  position: relative;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 70% 30%, rgba(139, 195, 74, 0.1), transparent 60%);
  border-radius: 15px;
  pointer-events: none;
}

.metric-card:hover {
  background: var(--glass-bg-hover);
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 25px rgba(76, 110, 82, 0.25);
  border-color: var(--accent-blue-light);
}

.metric-icon i {
  color: var(--accent-blue-primary);
  font-size: 1.8rem;
}

.metric-content {
  display: flex;
  flex-direction: column;
}

.metric-label {
  font-size: 0.85rem;
  color: var(--text-white-tertiary);
  font-weight: 500;
  text-transform: capitalize;
  letter-spacing: 0.3px;
}

.metric-value {
  font-size: 1.1rem;
  color: var(--text-white-primary);
  font-weight: 600;
}

/* User Count Badge */
.user-count {
  background: linear-gradient(135deg, var(--accent-blue-primary), var(--accent-blue-secondary));
  color: white;
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 0.9rem;
  font-weight: 600;
  margin-left: auto;
  box-shadow: 0 4px 15px rgba(76, 110, 82, 0.3);
}

/* Users Container */
.users-container {
  max-height: 300px;
  overflow-y: auto;
}

.no-users {
  text-align: center;
  padding: 30px 20px;
  color: var(--text-white-tertiary);
  position: relative;
}

.no-users::before {
  content: '🌱';
  display: block;
  font-size: 2rem;
  margin-bottom: 10px;
}

.no-users i {
  font-size: 2rem;
  margin-bottom: 10px;
  color: var(--accent-blue-light);
}

.user-card {
  background: var(--glass-bg-tertiary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 15px;
  padding: 15px;
  margin-bottom: 10px;
  transition: var(--transition-smooth);
  box-shadow: 0 4px 20px rgba(76, 110, 82, 0.15);
  border: 2px solid rgba(139, 195, 74, 0.2);
  position: relative;
}

.user-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 80% 20%, rgba(139, 195, 74, 0.1), transparent 60%);
  border-radius: 15px;
  pointer-events: none;
}

.user-card:hover {
  background: var(--glass-bg-hover);
  transform: translateX(5px) scale(1.02);
  box-shadow: 0 8px 25px rgba(76, 110, 82, 0.25);
  border-color: var(--accent-blue-light);
}

.user-card.current-user {
  background: linear-gradient(135deg, var(--glass-bg-tertiary), rgba(76, 110, 82, 0.3));
  box-shadow: 0 8px 25px rgba(76, 110, 82, 0.4);
  border-color: var(--accent-blue-primary);
}

.user-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.user-header i {
  color: var(--accent-blue-primary);
  font-size: 1.2rem;
}

.username {
  color: var(--text-white-primary);
  font-weight: 600;
  flex-grow: 1;
}

.session-badge {
  background: linear-gradient(135deg, var(--accent-blue-primary), var(--accent-blue-secondary));
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 0.75rem;
  font-weight: 500;
}

.user-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.85rem;
}

.last-activity {
  color: var(--text-white-tertiary);
  font-style: italic;
}

.activity-type {
  color: var(--text-white-secondary);
  background: var(--glass-bg-secondary);
  padding: 2px 8px;
  border-radius: 8px;
  font-weight: 500;
  border: 1px solid var(--accent-blue-light);
}

/* Tool Cards Styles */
.tool-card {
  background: var(--glass-bg-secondary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 20px;
  padding: 20px;
  margin-bottom: 15px;
  cursor: pointer;
  transition: var(--transition-smooth);
  box-shadow: var(--glass-shadow);
  position: relative;
  overflow: hidden;
  border: var(--glass-border);
}

.tool-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 70% 30%, rgba(139, 195, 74, 0.1), transparent 60%);
  border-radius: 20px;
  pointer-events: none;
}

.tool-card:hover {
  background: var(--glass-bg-tertiary);
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 15px 40px rgba(76, 110, 82, 0.3);
  border-color: var(--accent-blue-light);
}

.tool-card h3 {
  color: var(--accent-blue-primary);
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 10px;
  text-shadow: 0 2px 10px rgba(76, 110, 82, 0.2);
  position: relative;
}

.tool-card h3::after {
  content: '🌿';
  position: absolute;
  right: -25px;
  top: 0;
  font-size: 0.8em;
}

.tool-card p {
  color: var(--text-white-secondary);
  font-size: 0.95rem;
  line-height: 1.5;
}

/* Session Cards Styles */
.session-card {
  background: var(--glass-bg-secondary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 15px;
  padding: 18px;
  margin-bottom: 12px;
  transition: var(--transition-smooth);
  box-shadow: var(--glass-shadow);
  position: relative;
  overflow: hidden;
  border: var(--glass-border);
}

.session-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, var(--accent-blue-primary), var(--accent-blue-secondary));
  transition: var(--transition-smooth);
  border-radius: 0 0 0 15px;
}

.session-card:hover {
  background: var(--glass-bg-tertiary);
  transform: translateX(5px) scale(1.01);
  box-shadow: 0 8px 30px rgba(76, 110, 82, 0.3);
}

.session-card:hover::before {
  width: 6px;
  background: linear-gradient(to bottom, var(--accent-blue-secondary), var(--accent-blue-light));
}

.session-card .title {
  color: var(--text-white-primary);
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 10px;
}

.session-card .time {
  color: var(--text-white-tertiary);
  font-size: 0.85rem;
  background: var(--glass-bg-primary);
  padding: 4px 10px;
  border-radius: 15px;
  display: inline-block;
  margin-bottom: 10px;
  border: 1px solid var(--accent-blue-light);
}

.progress-counter {
  color: var(--accent-blue-primary);
  font-weight: 600;
  font-size: 0.9rem;
  background: var(--glass-bg-primary);
  padding: 4px 10px;
  border-radius: 12px;
  display: inline-block;
  margin-bottom: 10px;
  border: 1px solid var(--accent-blue-light);
}

/* Session Action Buttons */
.refresh-button, .kill-button, .download-button {
  background: var(--glass-bg-primary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  color: var(--accent-blue-primary);
  border: 2px solid var(--accent-blue-light);
  padding: 8px 16px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-smooth);
  margin-right: 8px;
  margin-top: 8px;
  text-transform: capitalize;
  letter-spacing: 0.3px;
}

.refresh-button:hover {
  background: var(--accent-blue-primary);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(76, 110, 82, 0.4);
  border-color: var(--accent-blue-primary);
}

.kill-button:hover {
  background: #d32f2f;
  color: white;
  border-color: #d32f2f;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(211, 47, 47, 0.4);
}

.download-button:hover {
  background: #388e3c;
  color: white;
  border-color: #388e3c;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(56, 142, 60, 0.4);
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--glass-bg-secondary);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, var(--accent-blue-primary), var(--accent-blue-secondary));
  border-radius: 10px;
  transition: var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, var(--accent-blue-secondary), var(--accent-blue-light));
}

/* Responsive Design */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
  }

  .right-section {
    width: 100%;
    flex-direction: row;
  }

  .system-performance-section, .active-users-section {
    flex: 1;
  }
}

@media (max-width: 768px) {
  .side-bar-container {
    width: 80px;
  }

  .main-content {
    padding: 15px;
  }

  .right-section {
    flex-direction: column;
  }

  .performance-metrics {
    grid-template-columns: 1fr;
  }
}
