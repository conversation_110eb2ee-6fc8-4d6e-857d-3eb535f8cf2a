/* Original Glassmorphism Theme - CSS Variables */
:root {
  /* Glassmorphism Colors */
  --glass-bg-primary: rgba(255, 255, 255, 0.1);
  --glass-bg-secondary: rgba(255, 255, 255, 0.05);
  --glass-bg-tertiary: rgba(255, 255, 255, 0.15);
  --glass-bg-hover: rgba(255, 255, 255, 0.2);

  /* Black and Blue Accents */
  --accent-black: #000000;
  --accent-blue-primary: #1e3a8a;
  --accent-blue-secondary: #3b82f6;
  --accent-blue-light: #60a5fa;
  --accent-blue-dark: #1e40af;

  /* White Font Shades */
  --text-white-primary: rgba(255, 255, 255, 0.95);
  --text-white-secondary: rgba(255, 255, 255, 0.8);
  --text-white-tertiary: rgba(255, 255, 255, 0.6);
  --text-white-muted: rgba(255, 255, 255, 0.4);

  /* Glassmorphism Effects */
  --glass-blur: blur(20px);
  --glass-blur-light: blur(10px);
  --glass-border: 1px solid rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  --glass-shadow-hover: 0 12px 40px rgba(0, 0, 0, 0.4);

  /* Transitions */
  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.2s ease;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}


body {
  font-family: 'montserrat', sans-serif;
  background-image: url("pexels-stacey-gabrielle-koenitz-rozells-2425011.jpg");
  background-size: cover;
  background-repeat: no-repeat;
  background-attachment: fixed;
  margin: 0;
  display: flex;
  flex-direction: row;
  min-height: 100vh;
  color: var(--text-white-primary);
  overflow-x: hidden;
}

.side-bar-container {
  display: flex;
  height: 100vh;
  width: 100px;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
}

.side-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 40px;
  padding: 25px;
  width: 60px;
  height: 50vh;
  border-radius: 30px;
  background: var(--glass-bg-primary);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  box-shadow: var(--glass-shadow);
  transition: var(--transition-smooth);
}

.side-bar:hover {
  background: var(--glass-bg-hover);
  box-shadow: var(--glass-shadow-hover);
  transform: translateY(-2px);
}

.tool-category {
  color: var(--text-white-primary);
  display: flex;
  padding: 12px;
  border-radius: 50%;
  cursor: pointer;
  transition: var(--transition-smooth);
  background: var(--glass-bg-secondary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  position: relative;
  overflow: hidden;
}

.tool-category::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, var(--accent-blue-light), var(--accent-blue-secondary));
  opacity: 0;
  transition: var(--transition-smooth);
  z-index: -1;
}

.tool-category:hover {
  background: var(--glass-bg-tertiary);
  color: var(--text-white-primary);
  transform: scale(1.1);
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.4);
}

.tool-category:hover::before {
  opacity: 0.3;
}

.tool-category[aria-selected="true"] {
  background: var(--accent-blue-secondary);
  color: var(--text-white-primary);
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.6);
}

.main-content {
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  padding: 20px;
  gap: 20px;
}

.tools-cards-section {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 10px;
  border-radius: 25px;
  overflow-y: auto;
  max-width: 270px;
}

.tools-cards-section h2 {
  align-self: flex-start; /* top-left */
  margin-bottom: 10px;
}

.tools-cards-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center; /* center everything inside this wrapper */
}


.middle-section {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 30px;
  overflow-y: auto;
}


.sessions-section {
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  padding: 30px;
  overflow-y: auto;
  gap: 20px;
}


.finished-sessions, .running-sessions {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 25px;
  background: var(--glass-bg-secondary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  overflow-y: auto;
  transition: var(--transition-smooth);
}

.finished-sessions:hover, .running-sessions:hover {
  background: var(--glass-bg-tertiary);
  transform: translateY(-2px);
}

/* Typography Styles */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-white-primary);
  font-weight: 600;
  margin-bottom: 20px;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

h1 {
  font-size: 2.5rem;
  background: linear-gradient(135deg, var(--text-white-primary), var(--accent-blue-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

h2 {
  font-size: 2rem;
  color: var(--text-white-primary);
}

h3 {
  font-size: 1.5rem;
  color: var(--text-white-secondary);
}

/* AutoSpace Section */
.autospace-section {
  border-radius: 20px;
  padding: 25px;
  margin-bottom: 20px;
}

.default-view {
  color: var(--text-white-tertiary);
  font-size: 1.1rem;
  text-align: center;
  padding: 40px 20px;
  background: var(--glass-bg-secondary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 15px;
  font-style: italic;
}

.selection-view {
  display: flex;
  flex-direction: column;
  gap: 20px;
  background: var(--glass-bg-secondary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 15px;
  padding: 25px;
}

.tool-name {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-white-primary);
  margin-bottom: 10px;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.tool-description {
  font-size: 1.1rem;
  color: var(--text-white-secondary);
  line-height: 1.6;
  margin-bottom: 20px;
}

.tool-buttons {
  display: flex;
  flex-direction: row;
  gap: 15px;
  align-items: flex-start;
}

/* Button Styles */
.run-button, .download-sample-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 15px 30px;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-white-primary);
  background: var(--glass-bg-primary);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: none;
  border-radius: 50px;
  cursor: pointer;
  transition: var(--transition-smooth);
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: var(--glass-shadow);
  position: relative;
  overflow: hidden;
}

.run-button::before, .download-sample-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, var(--accent-blue-secondary), var(--accent-blue-light));
  opacity: 0;
  transition: var(--transition-smooth);
  z-index: -1;
}

.run-button:hover, .download-sample-button:hover {
  background: var(--glass-bg-hover);
  transform: translateY(-3px) scale(1.05);
  box-shadow: var(--glass-shadow-hover);
  color: var(--text-white-primary);
}

.run-button:hover::before, .download-sample-button:hover::before {
  opacity: 0.3;
}

.run-button:active, .download-sample-button:active {
  transform: translateY(-1px) scale(1.02);
}

/* File Input Styles */
.file-label {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 15px 30px;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-white-primary);
  background: var(--glass-bg-primary);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border-radius: 50px;
  cursor: pointer;
  transition: var(--transition-smooth);
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: var(--glass-shadow);
  position: relative;
  overflow: hidden;
}

.file-label::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, var(--accent-blue-primary), var(--accent-blue-secondary));
  opacity: 0;
  transition: var(--transition-smooth);
  z-index: -1;
}

.file-label:hover {
  background: var(--glass-bg-hover);
  transform: translateY(-3px) scale(1.05);
  box-shadow: var(--glass-shadow-hover);
}

.file-label:hover::before {
  opacity: 0.3;
}

#fileInput {
  display: none;
}

/* Checkbox and Label Styles */
input[type="checkbox"] {
  appearance: none;
  width: 20px;
  height: 20px;
  background: var(--glass-bg-secondary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 4px;
  cursor: pointer;
  position: relative;
  transition: var(--transition-smooth);
  margin-right: 10px;
}

input[type="checkbox"]:checked {
  background: var(--accent-blue-secondary);
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
}

input[type="checkbox"]:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--text-white-primary);
  font-weight: bold;
  font-size: 14px;
}

label[for="mode"] {
  color: var(--text-white-secondary);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-fast);
}

label[for="mode"]:hover {
  color: var(--text-white-primary);
}

/* Right Section Styles */
.right-section {
  display: flex;
  flex-direction: column;
  width: 350px;
  gap: 20px;
  flex-grow: .5;
}

.system-performance-section, .active-users-section {
  padding: 25px;
}

.box-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-white-primary);
  margin-bottom: 20px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.box-title i {
  color: var(--accent-blue-light);
  font-size: 1.5rem;
}

.performance-box, .users-box {
  border-radius: 15px;
  padding: 20px;
}

.performance-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.metric-card {
  background: var(--glass-bg-tertiary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 12px;
  padding: 15px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: var(--transition-smooth);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.metric-card:hover {
  background: var(--glass-bg-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.metric-icon i {
  color: var(--accent-blue-light);
  font-size: 1.8rem;
}

.metric-content {
  display: flex;
  flex-direction: column;
}

.metric-label {
  font-size: 0.85rem;
  color: var(--text-white-tertiary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metric-value {
  font-size: 1.1rem;
  color: var(--text-white-primary);
  font-weight: 600;
}

/* User Count Badge */
.user-count {
  background: var(--accent-blue-secondary);
  color: var(--text-white-primary);
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  margin-left: auto;
}

/* Users Container */
.users-container {
  max-height: 300px;
  overflow-y: auto;
}

.no-users {
  text-align: center;
  padding: 30px 20px;
  color: var(--text-white-tertiary);
}

.no-users i {
  font-size: 2rem;
  margin-bottom: 10px;
  color: var(--accent-blue-light);
}

.user-card {
  background: var(--glass-bg-tertiary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 10px;
  transition: var(--transition-smooth);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.user-card:hover {
  background: var(--glass-bg-hover);
  transform: translateX(5px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.user-card.current-user {
  background: linear-gradient(135deg, var(--glass-bg-tertiary), var(--accent-blue-primary));
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
}

.user-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.user-header i {
  color: var(--accent-blue-light);
  font-size: 1.2rem;
}

.username {
  color: var(--text-white-primary);
  font-weight: 600;
  flex-grow: 1;
}

.session-badge {
  background: var(--accent-blue-secondary);
  color: var(--text-white-primary);
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 0.75rem;
  font-weight: 500;
}

.user-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.85rem;
}

.last-activity {
  color: var(--text-white-tertiary);
  font-style: italic;
}

.activity-type {
  color: var(--text-white-secondary);
  background: var(--glass-bg-secondary);
  padding: 2px 8px;
  border-radius: 8px;
  font-weight: 500;
}

/* Tool Cards Styles */
.tool-card {
  background: var(--glass-bg-secondary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 15px;
  cursor: pointer;
  transition: var(--transition-smooth);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.tool-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--accent-blue-primary), var(--accent-blue-secondary));
  opacity: 0;
  transition: var(--transition-smooth);
  z-index: -1;
}

.tool-card:hover {
  background: var(--glass-bg-tertiary);
  transform: translateY(-5px) scale(1.02);
  box-shadow: var(--glass-shadow-hover);
}

.tool-card:hover::before {
  opacity: 0.1;
}

.tool-card h3 {
  color: var(--text-white-primary);
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 10px;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.tool-card p {
  color: var(--text-white-secondary);
  font-size: 0.95rem;
  line-height: 1.5;
}

/* Session Cards Styles */
.session-card {
  background: var(--glass-bg-secondary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 12px;
  padding: 18px;
  margin-bottom: 12px;
  transition: var(--transition-smooth);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.session-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--accent-blue-secondary);
  transition: var(--transition-smooth);
}

.session-card:hover {
  background: var(--glass-bg-tertiary);
  transform: translateX(5px);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.3);
}

.session-card:hover::before {
  width: 6px;
  background: var(--accent-blue-light);
}

.session-card .title {
  color: var(--text-white-primary);
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 10px;
}

.session-card .time {
  color: var(--text-white-tertiary);
  font-size: 0.85rem;
  background: var(--glass-bg-primary);
  padding: 4px 10px;
  border-radius: 15px;
  display: inline-block;
  margin-bottom: 10px;
}

.progress-counter {
  color: var(--accent-blue-light);
  font-weight: 600;
  font-size: 0.9rem;
  background: var(--glass-bg-primary);
  padding: 4px 10px;
  border-radius: 12px;
  display: inline-block;
  margin-bottom: 10px;
}

/* Session Action Buttons */
.refresh-button, .kill-button, .download-button {
  background: var(--glass-bg-primary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  color: var(--text-white-primary);
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-smooth);
  margin-right: 8px;
  margin-top: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.refresh-button:hover {
  background: var(--accent-blue-secondary);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
}

.kill-button:hover {
  background: #ef4444;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
}

.download-button:hover {
  background: #10b981;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--glass-bg-secondary);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: var(--accent-blue-secondary);
  border-radius: 10px;
  transition: var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-blue-light);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
  }

  .right-section {
    width: 100%;
    flex-direction: row;
  }

  .system-performance-section, .active-users-section {
    flex: 1;
  }
}

@media (max-width: 768px) {
  .side-bar-container {
    width: 80px;
  }

  .main-content {
    padding: 15px;
  }

  .right-section {
    flex-direction: column;
  }

  .performance-metrics {
    grid-template-columns: 1fr;
  }
}
