/* Unified Theme Variables */
:root {
    /* Primary Colors */
    --color-primary: #667eea;
    --color-primary-dark: #5a6fd8;
    --color-accent: #764ba2;
    --color-accent-dark: #6a4190;

    /* Background Colors */
    --color-bg: #f5f5f5;
    --color-bg-secondary: #f8f9ff;
    --color-bg-tertiary: #e8f0ff;
    --color-white: #fff;

    /* Text Colors */
    --color-text: #2a2a2a;
    --color-text-secondary: #666;
    --color-text-light: #999;
    --color-text-white: #fff;

    /* Border and Shadow */
    --color-border: #e0e0e0;
    --color-border-light: #f0f0f0;

    /* Status Colors */
    --color-success: #28a745;
    --color-error: #dc3545;
    --color-warning: #ffc107;
    --color-info: #17a2b8;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--color-primary), var(--color-accent));
    --gradient-auth: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-card: linear-gradient(135deg, var(--color-bg-secondary), var(--color-bg-tertiary));

    /* Spacing System */
    --spacing-xs: 5px;
    --spacing-sm: 8px;
    --spacing-md: 10px;
    --spacing-lg: 15px;
    --spacing-xl: 20px;
    --spacing-xxl: 30px;
    --spacing-3xl: 40px;
    --spacing-4xl: 60px;

    /* Typography Scale */
    --font-size-xs: 10px;
    --font-size-sm: 12px;
    --font-size-md: 14px;
    --font-size-lg: 16px;
    --font-size-xl: 18px;
    --font-size-2xl: 24px;
    --font-size-3xl: 32px;
    --font-size-4xl: 42px;
    --font-size-5xl: 48px;

    /* Font Weights */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    /* Line Heights */
    --line-height-tight: 1.2;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.6;
    --line-height-loose: 1.8;

    /* Letter Spacing */
    --letter-spacing-tight: -0.5px;
    --letter-spacing-normal: 0;
    --letter-spacing-wide: 0.5px;
    --letter-spacing-wider: 1px;
    --letter-spacing-widest: 2px;

    /* Border Radius */
    --border-radius-none: 0;
    --border-radius-sm: 2px;
    --border-radius-md: 5px;
    --border-radius-lg: 10px;
    --border-radius-xl: 15px;
    --border-radius-2xl: 20px;
    --border-radius-full: 50%;
    --border-radius-pill: 25px;

    /* Shadows */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-lg: 0 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 8px 16px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0 20px 40px rgba(0, 0, 0, 0.2);
    --shadow-nav: 0 2px 10px rgba(0, 0, 0, 0.1);

    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;

    /* Layout */
    --border-standard: 1px solid var(--color-border);
    --box-padding: var(--spacing-lg);
    --section-gap: 0;
    --card-gap: var(--spacing-md);
    --nav-height: 60px;
    --footer-height: auto;
}

/* Base styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Montserrat';
    background-color: var(--color-bg);
    color: var(--color-text);
    line-height: var(--line-height-relaxed);
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-normal);
    margin: 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
a img{
    width: 150px;

}
/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    margin-bottom: var(--spacing-md);
    color: var(--color-text);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-md); }

p {
    margin-bottom: var(--spacing-md);
    line-height: var(--line-height-relaxed);
}

a {
    color: var(--color-primary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--color-primary-dark);
}

/* Unified Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--border-radius-md);
    font-family: inherit;
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-medium);
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    white-space: nowrap;
    user-select: none;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--color-text-white);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: var(--color-text-white);
}

.btn-secondary {
    background: var(--color-white);
    color: var(--color-text);
    border: var(--border-standard);
}

.btn-secondary:hover {
    background: var(--color-bg-secondary);
    color: var(--color-text);
}

.btn-pill {
    border-radius: var(--border-radius-pill);
    padding: var(--spacing-sm) var(--spacing-xl);
}

.btn-lg {
    padding: var(--spacing-lg) var(--spacing-xxl);
    font-size: var(--font-size-lg);
}

.btn-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
}

/* Unified Navigation Styles */
.nav-header {
    background: var(--color-white);
    padding: var(--spacing-lg) var(--spacing-xxl);
    box-shadow: var(--shadow-nav);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: var(--nav-height);
}

.nav-logo {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-primary);
    text-decoration: none;
    letter-spacing: var(--letter-spacing-widest);
}

.nav-links {
    display: flex;
    list-style: none;
    gap: var(--spacing-xxl);
    margin: 0;
    padding: 0;
}

.nav-links a {
    color: var(--color-text);
    font-weight: var(--font-weight-medium);
    transition: color var(--transition-normal);
}

.nav-links a:hover {
    color: var(--color-primary);
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.user-info {
    color: var(--color-text-secondary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

/* Unified Form Styles */
.form-group {
    position: relative;
    margin-bottom: var(--spacing-xl);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: var(--font-weight-medium);
    color: var(--color-text);
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    border: var(--border-standard);
    border-radius: var(--border-radius-md);
    font-family: inherit;
    font-size: var(--font-size-md);
    background: var(--color-white);
    transition: border-color var(--transition-normal);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Unified Layout Styles */
.container {
    display: grid;
    grid-template-rows: auto 1fr auto;
    min-height: 100vh;
    width: 100%;
}

.main-content {
    margin-top: var(--nav-height);
    padding: var(--spacing-xl);
}

/* Unified Card Styles */
.card {
    background: var(--color-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    padding: var(--spacing-xl);
    transition: box-shadow var(--transition-normal);
}

.card:hover {
    box-shadow: var(--shadow-lg);
}

.card-header {
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-lg);
    border-bottom: var(--border-standard);
}

.card-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text);
    margin-bottom: var(--spacing-sm);
}

.card-subtitle {
    font-size: var(--font-size-md);
    color: var(--color-text-secondary);
    margin-bottom: 0;
}

.card-body {
    padding: 0;
}

.card-footer {
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: var(--border-standard);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Unified Grid System */
.grid {
    display: grid;
    gap: var(--spacing-lg);
}

.grid-cols-1 { grid-template-columns: 1fr; }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

@media (max-width: 768px) {
    .grid-cols-2,
    .grid-cols-3,
    .grid-cols-4 {
        grid-template-columns: 1fr;
    }
}

/* Unified Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--color-primary); }
.text-secondary { color: var(--color-text-secondary); }
.text-success { color: var(--color-success); }
.text-error { color: var(--color-error); }
.text-warning { color: var(--color-warning); }

.bg-primary { background-color: var(--color-primary); }
.bg-secondary { background-color: var(--color-bg-secondary); }
.bg-white { background-color: var(--color-white); }

.rounded { border-radius: var(--border-radius-md); }
.rounded-lg { border-radius: var(--border-radius-lg); }
.rounded-full { border-radius: var(--border-radius-full); }

.shadow { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }
.mb-6 { margin-bottom: var(--spacing-xxl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }
.mt-6 { margin-top: var(--spacing-xxl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }
.p-6 { padding: var(--spacing-xxl); }

/* Loading States */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--border-radius-full);
    border-top-color: var(--color-primary);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Common styles */
.section-container {
    display: flex;
    flex-direction: column;
    gap: var(--section-gap);
    background-color: var(--color-bg);
}

.box {
    padding: var(--box-padding);
    background-color: var(--color-white);
    border-bottom: var(--border-standard);
    border-left: var(--border-standard);
    margin: 10px;
    border-radius: var(--border-radius-sm);
    transition: box-shadow 0.2s ease;
}



.box-title {
    font-size: var(--font-size-md);
    font-weight: bold;
    letter-spacing: var(--letter-spacing-standard);
    color: var(--color-text);
    margin-bottom: var(--spacing-md);
    text-transform: uppercase;
    padding-bottom: var(--spacing-xs);
    border-bottom: 1px solid var(--color-border);
    display: inline-block;
}

.secondary-text {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    letter-spacing: var(--letter-spacing-standard);
}

/* Legacy top-bar styles - now using unified nav-header */
.top-bar {
    height: var(--nav-height);
    margin-bottom: var(--spacing-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 var(--spacing-md);
    background-color: var(--color-white);
    border-bottom: var(--border-standard);
    box-shadow: var(--shadow-nav);
}

.logo {
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-xl);
    letter-spacing: var(--letter-spacing-widest);
    color: var(--color-primary);
}

.top-menu {
    display: flex;
    gap:50px
}

nav ul {
    display: flex;
    list-style: none;
    align-items: center;
    gap: 90px;
}

nav ul li {
    margin-right: var(--spacing-xxl);
}

nav ul li a {
    text-decoration: none;
    color: var(--color-text);
    font-size: var(--font-size-md);
    letter-spacing: var(--letter-spacing-standard);
    transition: color 0.3s;
}

nav ul li a:hover {
    color: var(--color-text-secondary);
}

.account {
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: var(--font-size-md);
    color: var(--color-text-secondary);
}

/* Legacy logout button - now using unified btn classes */
.logout-button {
    background: none;
    border: 1px solid var(--color-text-secondary);
    color: var(--color-text-secondary);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    font-size: var(--font-size-sm);
    transition: all var(--transition-normal);
}

.logout-button:hover {
    background: var(--color-text-secondary);
    color: var(--color-white);
}

/* Content Area */
.content {
    display: grid;
    grid-template-columns: 200px 400px 1.5fr 1fr;
    grid-template-rows: 1fr;
    min-height: calc(100vh - 120px);
    background-color: var(--color-bg);
}

/* Side Bar */
.side-bar {
    margin-top: 10px;
    display: flex;
    flex-direction: column;
    border-right: var(--border-standard);
}

.tabs {
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 150px;
}

.tab {
    width: 198px;
    display: flex;
    align-items: center;
    height: 60px;
    font-size: var(--font-size-sm);
    color: var(--color-text);
    letter-spacing: var(--letter-spacing-standard);
    cursor: pointer;
    border-radius: var(--border-radius-sm);
    border-bottom: 2px solid var(--color-text);
    border-top: 2px solid var(--color-text);
    transition: all 0.3s ease;
    padding: 0 15px;
    gap: 10px;
}

.tab.active {
    background-color: rgba(0, 0, 0, 0.08);
    border-left: 4px solid var(--color-text);
}

.tab i {
    font-size: var(--font-size-lg);
    width: 20px;
    text-align: center;
}

.tab span {
    font-size: var(--font-size-md);
    font-weight: 600;
    color: var(--color-text);
    transition: all 0.2s ease;
}

.tab:hover {
    background-color: rgba(0, 0, 0, 0.05);
    transform: translateX(5px);
}

.tab:hover span {
    color: var(--color-text-secondary);
}

/* Tool Cards Section */
.tools-section {
    border-right: var(--border-standard);
}

.tool-card {
    padding: 15px;
    min-height: 120px;
    background-color: var(--color-white);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    border-radius: var(--border-radius-sm);
    transition: all 0.3s ease;
    margin-bottom: var(--spacing-md);
    border: 2px solid transparent;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.tool-card:hover {
    border-color: var(--color-text-secondary);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.tool-card.selected {
    border-color: var(--color-text);
    background-color: rgba(0, 0, 0, 0.02);
}

.tool-card-header {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
}

.tool-card h3 {
    margin: 0;
    font-size: var(--font-size-md);
    font-weight: 600;
    color: var(--color-text);
    line-height: 1.3;
}

.tool-card-preview {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    line-height: 1.4;
    flex-grow: 1;
}

.no-tools {
    text-align: center;
    padding: 40px 20px;
    color: var(--color-text-secondary);
}

.no-tools i {
    font-size: 2rem;
    margin-bottom: 10px;
    display: block;
}
.section h3{
    padding: 10px;
}

/* Middle Section */
.middle-section {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-right: var(--border-standard);
}

.section{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex-grow: 1;
}

.description-box .tool-description{
    margin:0px;

}

.description-box {
    display: flex;
    flex-direction: column;
    height: 100%;
    max-height: 400px;
}

.tool-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--color-border);
}

.tool-name {
    font-weight: 600;
    font-size: var(--font-size-lg);
    margin: 0;
}

.tool-id {
    background: var(--color-text);
    color: var(--color-white);
    padding: 4px 12px;
    border-radius: 15px;
    font-size: var(--font-size-xs);
    font-weight: 600;
    letter-spacing: 1px;
}

.tool-description {
    margin-bottom: var(--spacing-lg);
    white-space: pre-line;
    line-height: 1.6;
    color: var(--color-text-secondary);
}

.file-info {
    display: none;
    align-items: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
    padding: var(--spacing-sm);
    background: rgba(0, 0, 0, 0.02);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
}

.file-info i {
    color: var(--color-text);
}

/* Fixed Header and Scrollable Content Layout */
.fixed-header {
    position: sticky;
    top: 0;
    background: var(--color-white);
    z-index: 10;
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--color-border);
    margin-bottom: var(--spacing-sm);
}

.scrollable-content {
    flex: 1;
    overflow-y: auto;
    padding-right: 5px;
    margin-bottom: var(--spacing-sm);
}

.fixed-footer {
    position: sticky;
    bottom: 0;
    background: var(--color-white);
    z-index: 10;
    padding-top: var(--spacing-sm);
    border-top: 1px solid var(--color-border);
}

/* Custom scrollbar for scrollable content */
.scrollable-content::-webkit-scrollbar {
    width: 6px;
}

.scrollable-content::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
}

.scrollable-content::-webkit-scrollbar-thumb {
    background: var(--color-text-secondary);
    border-radius: 3px;
}

.scrollable-content::-webkit-scrollbar-thumb:hover {
    background: var(--color-text);
}

.button-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
    align-items: center;
}

.mode-selection {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.mode-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
}

.mode-text {
    font-weight: 500;
}
.progress-section{
    flex-grow: 1;
}
.smaller-container{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}
.progress-box, .finished-box {
    flex-grow:  1;
    border-radius: var(--border-radius-sm);
}
.progress-box, .finished-box {
    display: flex;
    flex-direction: column;
    height: 350px;
}

.sessions-container {
    flex: 1;
    overflow-y: auto;
    padding-right: 5px;
}

.session-card {
    display: flex;
    flex-direction: column;
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    background: var(--color-white);
    border-radius: var(--border-radius-sm);
    border-left: 4px solid var(--color-border);
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.session-card:hover {
    cursor: pointer;
    border-color: var(--color-text-secondary);
    transform: translateX(3px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.session-card.pending {
    border-left-color: #ffc107;
}

.session-card.running {
    border-left-color: #17a2b8;
    animation: pulse 2s infinite;
}

.session-card.done {
    border-left-color: #28a745;
}

.session-card.error {
    border-left-color: #dc3545;
}

.session-card.killed {
    border-left-color: #6c757d;
    opacity: 0.8;
}

.session-card.killed {
    border-left-color: #6c757d;
    opacity: 0.8;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.session-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.session-header .tool-name {
    font-weight: 600;
    font-size: var(--font-size-md);
    color: var(--color-text);
    margin: 0;
}

.session-time {
    font-size: var(--font-size-xs);
    color: var(--color-text-secondary);
    background: rgba(0, 0, 0, 0.05);
    padding: 2px 6px;
    border-radius: 10px;
}

.session-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

.session-indicator {
    display: flex;
    align-items: center;
}

.session-status {
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.session-mode {
    font-size: var(--font-size-xs);
    color: var(--color-text-secondary);
    background: rgba(0, 0, 0, 0.05);
    padding: 2px 6px;
    border-radius: 8px;
    text-transform: uppercase;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
    overflow: hidden;
    margin: var(--spacing-sm) 0;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #17a2b8, #28a745);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.progress-text {
    position: absolute;
    top: -20px;
    right: 0;
    font-size: var(--font-size-xs);
    color: var(--color-text-secondary);
    font-weight: 500;
}

.session-actions {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-sm);
    align-items: center;
}

.download-btn, .copy-path-btn {
    background: var(--color-text);
    color: var(--color-white);
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: var(--font-size-xs);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 4px;
}

.download-btn:hover {
    background: var(--color-text-secondary);
    transform: translateY(-1px);
}

.copy-path-btn {
    background: #6c757d;
}

.copy-path-btn:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

.copy-path-btn i {
    font-size: 10px;
}

.kill-btn {
    background: #dc3545;
}

.kill-btn:hover {
    background: #c82333;
    transform: translateY(-1px);
}

.retry-btn {
    background: #ffc107;
    color: #212529;
}

.retry-btn:hover {
    background: #e0a800;
    color: #212529;
    transform: translateY(-1px);
}

.error-message {
    background: #fee;
    color: #c33;
    padding: var(--spacing-sm);
    border-radius: 4px;
    font-size: var(--font-size-xs);
    margin-top: var(--spacing-sm);
    border-left: 3px solid #c33;
}

.no-sessions {
    text-align: center;
    padding: 30px 20px;
    color: var(--color-text-secondary);
}

.no-sessions i {
    font-size: 1.5rem;
    margin-bottom: 8px;
    display: block;
    opacity: 0.5;
}

.no-sessions p {
    font-size: var(--font-size-sm);
    margin: 0;
}

.session-count {
    background: var(--color-text);
    color: var(--color-white);
    padding: 2px 8px;
    border-radius: 10px;
    font-size: var(--font-size-xs);
    font-weight: 600;
    margin-left: var(--spacing-sm);
}

.session-limit {
    background: rgba(0, 0, 0, 0.1);
    color: var(--color-text-secondary);
    padding: 2px 6px;
    border-radius: 8px;
    font-size: var(--font-size-xs);
    font-weight: 400;
    margin-left: var(--spacing-xs);
    font-style: italic;
}

.output-file{
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    letter-spacing: var(--letter-spacing-standard);
}

.session-info, .click-copy{
    font-size: var(--font-size-md);
    color: var(--color-text-secondary);
    letter-spacing: var(--letter-spacing-standard);
    display: flex;
    flex-direction: row;
    gap: var(--spacing-md);
}

/* Right Section */
.right-section {
    background-color: var(--color-bg);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-right: var(--border-standard);
}

.visualization-box {
    flex-grow: 1;
}

.performance-metrics {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.metric {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-sm);
    background: rgba(0, 0, 0, 0.02);
    border-radius: var(--border-radius-sm);
}

.metric i {
    width: 20px;
    text-align: center;
    color: var(--color-text);
}

.metric-label {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    flex-grow: 1;
}

.metric-value {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--color-text);
}

.stats-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.stat-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-sm);
    background: rgba(0, 0, 0, 0.02);
    border-radius: var(--border-radius-sm);
}

.stat-item i {
    width: 20px;
    text-align: center;
    color: var(--color-text);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    flex-grow: 1;
}

.stat-value {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--color-text);
}

/* Active Users Section */
.users-section {
    display: flex;
    flex-direction: column;
    height: 350px;
}

.users-container {
    flex: 1;
    overflow-y: auto;
    padding-right: 5px;
}

.user-card {
    display: flex;
    flex-direction: column;
    padding: var(--spacing-sm);
    margin-bottom: var(--spacing-xs);
    background: var(--color-white);
    border-radius: var(--border-radius-sm);
    border-left: 3px solid var(--color-border);
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.user-card:hover {
    transform: translateX(3px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.user-card.current-user {
    border-left-color: #28a745;
    background: rgba(40, 167, 69, 0.05);
}

.user-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xs);
}

.username {
    font-weight: 600;
    font-size: var(--font-size-sm);
    color: var(--color-text);
    flex-grow: 1;
}

.session-badge {
    background: var(--color-text);
    color: var(--color-white);
    padding: 2px 6px;
    border-radius: 8px;
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.user-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-size-xs);
    color: var(--color-text-secondary);
}

.last-activity {
    font-style: italic;
}

.activity-type {
    text-transform: capitalize;
    background: rgba(0, 0, 0, 0.05);
    padding: 1px 4px;
    border-radius: 4px;
}

.no-users {
    text-align: center;
    padding: 30px 20px;
    color: var(--color-text-secondary);
}

.no-users i {
    font-size: 1.5rem;
    margin-bottom: 8px;
    display: block;
    opacity: 0.5;
}

.no-users p {
    font-size: var(--font-size-sm);
    margin: 0;
}

.users-box {
    height: 180px;
    border-radius: var(--border-radius-sm);
}

.user-count {
    color: var(--color-text-secondary);
    text-align: center;
    padding: var(--spacing-xl) 0;
    font-style: italic;
}

/* Footer */
footer {
    padding: var(--spacing-lg);
    text-align: center;
    background-color: var(--color-bg);
    border-top: var(--border-standard);
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    letter-spacing: var(--letter-spacing-standard);
}

.upload-button, .run-button, .download-sample-button {
    display: none;
    padding: 12px 24px;
    font-size: var(--font-size-sm);
    font-weight: 600;
    cursor: pointer;
    color: var(--color-text-secondary);
    border: 2px solid var(--color-text-secondary);
    background: transparent;
    transition: all 0.3s ease;
    border-radius: var(--border-radius-sm);
    outline: none;
    text-decoration: none;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.upload-button:hover, .run-button:hover, .download-sample-button:hover {
    background-color: var(--color-text-secondary);
    color: var(--color-white);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.upload-button:active, .run-button:active, .download-sample-button:active {
    transform: translateY(0);
}

.upload-button:disabled, .run-button:disabled, .download-sample-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    background: #6c757d !important;
    border-color: #6c757d !important;
}

.run-button:disabled {
    background: #dc3545 !important;
    border-color: #dc3545 !important;
    color: var(--color-white) !important;
}

.upload-button i, .run-button i, .download-sample-button i {
    margin-right: var(--spacing-sm);
}

/* Notification System */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--color-white);
    border-radius: var(--border-radius-sm);
    padding: 15px 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 12px;
    z-index: 1000;
    min-width: 300px;
    max-width: 500px;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification.success {
    border-left: 4px solid #28a745;
    color: #155724;
}

.notification.error {
    border-left: 4px solid #dc3545;
    color: #721c24;
}

.notification.warning {
    border-left: 4px solid #ffc107;
    color: #856404;
}

.notification.info {
    border-left: 4px solid #17a2b8;
    color: #0c5460;
}

.notification i {
    font-size: 1.2rem;
}

.notification .close-btn {
    background: none;
    border: none;
    cursor: pointer;
    color: inherit;
    opacity: 0.7;
    margin-left: auto;
    padding: 4px;
}

.notification .close-btn:hover {
    opacity: 1;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .content {
        grid-template-columns: 180px 350px 1fr 300px;
    }
}

@media (max-width: 992px) {
    .content {
        grid-template-columns: 150px 300px 1fr;
        grid-template-rows: auto auto;
    }

    .right-section {
        grid-column: 1 / -1;
        grid-row: 2;
        flex-direction: row;
        gap: 20px;
    }

    .visualization-section,
    .users-section {
        flex: 1;
    }
}

@media (max-width: 768px) {
    .content {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto auto;
    }

    .side-bar {
        grid-row: 1;
    }

    .tools-section {
        grid-row: 2;
    }

    .middle-section {
        grid-row: 3;
    }

    .right-section {
        grid-row: 4;
        flex-direction: column;
    }

    .tabs {
        flex-direction: row;
        width: 100%;
        overflow-x: auto;
    }

    .tab {
        width: auto;
        min-width: 120px;
        height: 40px;
        padding: 0 10px;
    }

    .top-bar {
        flex-direction: column;
        height: auto;
        padding: 10px;
        gap: 10px;
    }

    .top-menu nav ul {
        gap: 20px;
    }
}

/* Loading States */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: var(--color-text);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Accessibility Improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus States */
button:focus,
input:focus,
.tab:focus {
    outline: 2px solid var(--color-text);
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --color-border: #000;
        --color-text: #000;
        --color-text-secondary: #333;
    }
}

/* Admin Styles */
.admin-tab {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    border-left: 4px solid #c0392b;
}

.admin-tab.active {
    background: linear-gradient(135deg, #c0392b, #a93226);
    border-left: 4px solid #922b21;
}

.admin-section {
    display: none;
    padding: var(--spacing-xl);
}

.admin-overview h2 {
    color: #e74c3c;
    margin-bottom: var(--spacing-xl);
    font-size: var(--font-size-xl);
    font-weight: 700;
}

.admin-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xxl);
}

.stat-card {
    background: var(--color-white);
    border: var(--border-standard);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    box-shadow: var(--box-shadow);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    color: var(--color-white);
    background: linear-gradient(135deg, #3498db, #2980b9);
}

.stat-content h3 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--color-text);
    margin-bottom: var(--spacing-xs);
}

.stat-content p {
    color: var(--color-text-secondary);
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: var(--letter-spacing-wide);
}

.admin-users-section,
.admin-sessions-section {
    margin-bottom: var(--spacing-xxl);
}

.users-table-container,
.sessions-table-container {
    background: var(--color-white);
    border: var(--border-standard);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.users-table,
.sessions-table {
    width: 100%;
    border-collapse: collapse;
}

.users-table th,
.users-table td,
.sessions-table th,
.sessions-table td {
    padding: var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid var(--color-border);
}

.users-table th,
.sessions-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: var(--color-text);
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: var(--letter-spacing-wide);
}

.users-table tbody tr:hover,
.sessions-table tbody tr:hover {
    background: #f8f9fa;
}

.user-status {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.user-status.active {
    background: #d4edda;
    color: #155724;
}

.user-status.inactive {
    background: #f8d7da;
    color: #721c24;
}

.user-status.admin {
    background: #fff3cd;
    color: #856404;
}

.sessions-filter {
    padding: var(--spacing-lg);
    background: #f8f9fa;
    border-bottom: var(--border-standard);
    display: flex;
    gap: var(--spacing-lg);
}

.sessions-filter select {
    padding: var(--spacing-sm);
    border: var(--border-standard);
    border-radius: var(--border-radius-sm);
    background: var(--color-white);
    font-size: var(--font-size-sm);
}

.loading-row {
    text-align: center;
    color: var(--color-text-secondary);
    font-style: italic;
}

.admin-action-btn {
    padding: 4px 8px;
    border: none;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    cursor: pointer;
    margin-right: var(--spacing-xs);
    transition: all 0.2s ease;
}

.admin-action-btn.view {
    background: #17a2b8;
    color: white;
}

.admin-action-btn.kill {
    background: #dc3545;
    color: white;
}

.admin-action-btn:hover {
    opacity: 0.8;
    transform: translateY(-1px);
}

/* Kill button styling for user sessions */
.kill-btn {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.kill-btn:hover {
    background: linear-gradient(135deg, #c0392b, #a93226);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(231, 76, 60, 0.3);
}

.kill-btn:active {
    transform: translateY(0);
}