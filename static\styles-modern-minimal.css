/* Modern Minimalist Theme - Clean, contemporary interface inspired by health dashboard design
 * Design Concept: Flat design with clean typography, strategic yellow accents, and minimal visual noise
 * Key Features: 
 *   - No glassmorphism effects (no backdrop-filter or blur)
 *   - Clean geometric shapes with sharp edges
 *   - Light neutral palette with strategic yellow (#FFC928) accents
 *   - Subtle shadows and clean typography
 *   - Pill-shaped buttons and rounded cards
 * Color Palette: Light grays (#F7F7F8, #FFFFFF, #F3F4F6) with yellow primary (#FFC928)
 * Typography: Inter font family with clean, readable hierarchy
 */

:root {
  /* Modern Minimalist Colors - Based on health dashboard design */
  --glass-bg-primary: #FFFFFF;
  --glass-bg-secondary: #F7F7F8;
  --glass-bg-tertiary: #F3F4F6;
  --glass-bg-hover: #F3F4F6;

  /* Yellow Brand Accents */
  --accent-black: #161619;
  --accent-blue-primary: #FFC928;
  --accent-blue-secondary: #FFB800;
  --accent-blue-light: #FFE9A6;
  --accent-blue-dark: #E6B000;

  /* Clean Text Colors */
  --text-white-primary: #161619;
  --text-white-secondary: #40424A;
  --text-white-tertiary: #8B8D97;
  --text-white-muted: #8B8D97;

  /* Minimal Effects - No blur, clean shadows */
  --glass-blur: none;
  --glass-blur-light: none;
  --glass-border: 1px solid rgba(0,0,0,0.08);
  --glass-shadow: 0 8px 20px rgba(17,17,19,0.06);
  --glass-shadow-hover: 0 14px 32px rgba(17,17,19,0.10);

  /* Smooth Transitions */
  --transition-smooth: all 0.2s ease-out;
  --transition-fast: all 0.15s ease;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', 'Noto Sans Arabic', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', system-ui, sans-serif;
  background: var(--glass-bg-secondary);
  margin: 0;
  display: flex;
  flex-direction: row;
  min-height: 100vh;
  color: var(--text-white-primary);
  overflow-x: hidden;
  line-height: 1.5;
  font-weight: 400;
}

.side-bar-container {
  display: flex;
  height: 100vh;
  width: 100px;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
}

.side-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 24px;
  padding: 32px 16px;
  width: 72px;
  height: auto;
  min-height: 50vh;
  border-radius: 20px;
  background: var(--glass-bg-primary);
  box-shadow: var(--glass-shadow);
  transition: var(--transition-smooth);
  border: var(--glass-border);
}

.side-bar:hover {
  background: var(--glass-bg-primary);
  box-shadow: var(--glass-shadow-hover);
  transform: translateY(-2px);
}

.tool-category {
  color: #1C1B29;
  display: flex;
  padding: 12px;
  border-radius: 12px;
  cursor: pointer;
  transition: var(--transition-smooth);
  background: transparent;
  position: relative;
  width: 48px;
  height: 48px;
  align-items: center;
  justify-content: center;
}

.tool-category:hover {
  background: var(--glass-bg-tertiary);
  color: var(--text-white-primary);
  transform: scale(1.05);
}

.tool-category[aria-selected="true"] {
  background: var(--accent-blue-primary);
  color: #111113;
  font-weight: 600;
}

.tool-category[aria-selected="true"]::after {
  content: '';
  position: absolute;
  right: -8px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background: var(--accent-blue-primary);
  border-radius: 2px;
}

.main-content {
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  padding: 20px;
  gap: 20px;
}

.tools-cards-section {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 10px;
  border-radius: 20px;
  overflow-y: auto;
  max-width: 270px;
}

.tools-cards-section h2 {
  align-self: flex-start;
  margin-bottom: 16px;
}

.tools-cards-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.middle-section {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 32px;
  overflow-y: auto;
}

.sessions-section {
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  padding: 32px;
  overflow-y: auto;
  gap: 20px;
}

.finished-sessions, .running-sessions {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 32px;
  background: var(--glass-bg-primary);
  border-radius: 24px;
  box-shadow: var(--glass-shadow);
  overflow-y: auto;
  transition: var(--transition-smooth);
  border: var(--glass-border);
}

.finished-sessions:hover, .running-sessions:hover {
  background: var(--glass-bg-primary);
  transform: translateY(-2px);
  box-shadow: var(--glass-shadow-hover);
}

/* Typography Styles */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-white-primary);
  font-weight: 700;
  margin-bottom: 16px;
  letter-spacing: -0.2px;
}

h1 {
  font-size: 2.625rem;
  line-height: 3.25rem;
  font-weight: 800;
  color: var(--text-white-primary);
}

h2 {
  font-size: 1.75rem;
  line-height: 2.25rem;
  font-weight: 700;
  color: var(--text-white-primary);
}

h3 {
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-weight: 700;
  color: var(--text-white-secondary);
}

/* AutoSpace Section */
.autospace-section {
  border-radius: 24px;
  padding: 32px;
  margin-bottom: 20px;
}

.default-view {
  color: var(--text-white-tertiary);
  font-size: 1rem;
  line-height: 1.5rem;
  text-align: center;
  padding: 48px 24px;
  background: var(--glass-bg-primary);
  border-radius: 24px;
  border: var(--glass-border);
  box-shadow: var(--glass-shadow);
}

.selection-view {
  display: flex;
  flex-direction: column;
  gap: 24px;
  background: var(--glass-bg-primary);
  border-radius: 24px;
  padding: 32px;
  border: var(--glass-border);
  box-shadow: var(--glass-shadow);
}

.tool-name {
  font-size: 1.75rem;
  line-height: 2.25rem;
  font-weight: 700;
  color: var(--text-white-primary);
  margin-bottom: 8px;
  letter-spacing: -0.2px;
}

.tool-description {
  font-size: 1rem;
  line-height: 1.5rem;
  color: var(--text-white-secondary);
  margin-bottom: 24px;
  font-weight: 500;
}

.tool-buttons {
  display: flex;
  flex-direction: row;
  gap: 12px;
  align-items: flex-start;
}

/* Button Styles - Pill shaped like in design system */
.run-button, .download-sample-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  height: 40px;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  color: #111113;
  background: var(--accent-blue-primary);
  border: none;
  border-radius: 9999px;
  cursor: pointer;
  transition: var(--transition-smooth);
  box-shadow: 0 1px 2px rgba(17,17,19,0.04), 0 1px 1px rgba(17,17,19,0.06);
}

.run-button:hover, .download-sample-button:hover {
  background: var(--accent-blue-secondary);
  transform: translateY(-1px);
  box-shadow: var(--glass-shadow);
}

.run-button:active, .download-sample-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(17,17,19,0.04), 0 1px 1px rgba(17,17,19,0.06);
}

/* File Input Styles */
.file-label {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  height: 40px;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  color: var(--text-white-primary);
  background: var(--glass-bg-primary);
  border: var(--glass-border);
  border-radius: 9999px;
  cursor: pointer;
  transition: var(--transition-smooth);
  box-shadow: 0 1px 2px rgba(17,17,19,0.04), 0 1px 1px rgba(17,17,19,0.06);
}

.file-label:hover {
  background: var(--glass-bg-tertiary);
  transform: translateY(-1px);
  box-shadow: var(--glass-shadow);
}

#fileInput {
  display: none;
}

/* Checkbox and Label Styles */
input[type="checkbox"] {
  appearance: none;
  width: 18px;
  height: 18px;
  background: var(--glass-bg-primary);
  border: var(--glass-border);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  transition: var(--transition-smooth);
  margin-right: 8px;
}

input[type="checkbox"]:checked {
  background: var(--accent-blue-primary);
  border-color: var(--accent-blue-primary);
}

input[type="checkbox"]:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #111113;
  font-weight: 600;
  font-size: 12px;
}

label[for="mode"] {
  color: var(--text-white-secondary);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-fast);
  font-size: 0.875rem;
}

label[for="mode"]:hover {
  color: var(--text-white-primary);
}

/* Right Section Styles */
.right-section {
  display: flex;
  flex-direction: column;
  width: 350px;
  gap: 20px;
  flex-grow: .5;
}

.system-performance-section, .active-users-section {
  padding: 32px;
}

.box-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-weight: 700;
  color: var(--text-white-primary);
  margin-bottom: 24px;
  letter-spacing: -0.2px;
}

.box-title i {
  color: var(--accent-blue-primary);
  font-size: 1.5rem;
}

.performance-box, .users-box {
  border-radius: 24px;
  padding: 24px;
}

.performance-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.metric-card {
  background: var(--glass-bg-primary);
  border-radius: 16px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: var(--transition-smooth);
  box-shadow: var(--glass-shadow);
  border: var(--glass-border);
}

.metric-card:hover {
  background: var(--glass-bg-primary);
  transform: translateY(-2px);
  box-shadow: var(--glass-shadow-hover);
}

.metric-icon i {
  color: var(--accent-blue-primary);
  font-size: 1.5rem;
}

.metric-content {
  display: flex;
  flex-direction: column;
}

.metric-label {
  font-size: 0.75rem;
  line-height: 1rem;
  color: var(--text-white-tertiary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.2px;
}

.metric-value {
  font-size: 1.5rem;
  line-height: 2rem;
  color: var(--text-white-primary);
  font-weight: 700;
  font-variant-numeric: tabular-nums;
}

/* User Count Badge */
.user-count {
  background: var(--accent-blue-light);
  color: var(--text-white-primary);
  padding: 4px 10px;
  border-radius: 9999px;
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 500;
  margin-left: auto;
  height: 24px;
  display: flex;
  align-items: center;
}

/* Users Container */
.users-container {
  max-height: 300px;
  overflow-y: auto;
}

.no-users {
  text-align: center;
  padding: 32px 20px;
  color: var(--text-white-tertiary);
  font-size: 0.875rem;
}

.no-users i {
  font-size: 1.5rem;
  margin-bottom: 8px;
  color: var(--accent-blue-primary);
}

.user-card {
  background: var(--glass-bg-primary);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 12px;
  transition: var(--transition-smooth);
  box-shadow: var(--glass-shadow);
  border: var(--glass-border);
}

.user-card:hover {
  background: var(--glass-bg-primary);
  transform: translateY(-2px);
  box-shadow: var(--glass-shadow-hover);
}

.user-card.current-user {
  background: var(--glass-bg-primary);
  border-color: var(--accent-blue-primary);
  box-shadow: 0 0 0 2px var(--accent-blue-primary);
}

.user-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.user-header i {
  color: var(--accent-blue-primary);
  font-size: 1rem;
}

.username {
  color: var(--text-white-primary);
  font-weight: 600;
  flex-grow: 1;
  font-size: 0.875rem;
}

.session-badge {
  background: var(--accent-blue-light);
  color: var(--text-white-primary);
  padding: 2px 10px;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  height: 24px;
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
}

.last-activity {
  color: var(--text-white-tertiary);
  font-style: normal;
}

.activity-type {
  color: var(--text-white-secondary);
  background: var(--glass-bg-tertiary);
  padding: 2px 8px;
  border-radius: 6px;
  font-weight: 500;
}

/* Tool Cards Styles */
.tool-card {
  background: var(--glass-bg-primary);
  border-radius: 20px;
  padding: 24px;
  margin-bottom: 16px;
  cursor: pointer;
  transition: var(--transition-smooth);
  box-shadow: var(--glass-shadow);
  border: var(--glass-border);
}

.tool-card:hover {
  background: var(--glass-bg-primary);
  transform: translateY(-3px);
  box-shadow: var(--glass-shadow-hover);
}

.tool-card h3 {
  color: var(--text-white-primary);
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 600;
  margin-bottom: 8px;
  letter-spacing: -0.2px;
}

.tool-card p {
  color: var(--text-white-secondary);
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
}

/* Session Cards Styles */
.session-card {
  background: var(--glass-bg-primary);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 12px;
  transition: var(--transition-smooth);
  box-shadow: var(--glass-shadow);
  border: var(--glass-border);
  position: relative;
}

.session-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--accent-blue-primary);
  border-radius: 2px 0 0 2px;
  transition: var(--transition-smooth);
}

.session-card:hover {
  background: var(--glass-bg-primary);
  transform: translateY(-2px);
  box-shadow: var(--glass-shadow-hover);
}

.session-card:hover::before {
  width: 6px;
  background: var(--accent-blue-secondary);
}

.session-card .title {
  color: var(--text-white-primary);
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 600;
  margin-bottom: 8px;
}

.session-card .time {
  color: var(--text-white-tertiary);
  font-size: 0.75rem;
  line-height: 1rem;
  background: var(--glass-bg-tertiary);
  padding: 4px 8px;
  border-radius: 6px;
  display: inline-block;
  margin-bottom: 8px;
  font-weight: 500;
}

.progress-counter {
  color: var(--accent-blue-primary);
  font-weight: 600;
  font-size: 0.875rem;
  line-height: 1.25rem;
  background: var(--accent-blue-light);
  padding: 4px 8px;
  border-radius: 6px;
  display: inline-block;
  margin-bottom: 8px;
  font-variant-numeric: tabular-nums;
}

/* Session Action Buttons */
.refresh-button, .kill-button, .download-button {
  background: var(--glass-bg-primary);
  color: var(--text-white-primary);
  border: var(--glass-border);
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-smooth);
  margin-right: 8px;
  margin-top: 8px;
  height: 28px;
  display: inline-flex;
  align-items: center;
}

.refresh-button:hover {
  background: var(--accent-blue-primary);
  color: #111113;
  border-color: var(--accent-blue-primary);
  transform: translateY(-1px);
}

.kill-button:hover {
  background: #EF4444;
  color: white;
  border-color: #EF4444;
  transform: translateY(-1px);
}

.download-button:hover {
  background: #22C55E;
  color: white;
  border-color: #22C55E;
  transform: translateY(-1px);
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--glass-bg-tertiary);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--text-white-tertiary);
  border-radius: 3px;
  transition: var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-blue-primary);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
  }

  .right-section {
    width: 100%;
    flex-direction: row;
  }

  .system-performance-section, .active-users-section {
    flex: 1;
  }
}

@media (max-width: 768px) {
  .side-bar-container {
    width: 80px;
  }

  .main-content {
    padding: 16px;
  }

  .right-section {
    flex-direction: column;
  }

  .performance-metrics {
    grid-template-columns: 1fr;
  }

  .middle-section {
    padding: 20px;
  }

  .sessions-section {
    padding: 20px;
  }

  .system-performance-section, .active-users-section {
    padding: 20px;
  }
}
