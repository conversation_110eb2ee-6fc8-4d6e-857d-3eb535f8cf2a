from flask import Flask, request, render_template, send_file
import time
import random
import math
import json
import subprocess
import sys
import threading
import pandas as pd


app = Flask(__name__)

sessions_dict = {"U00001":{},"U00002":{},"U00003":{},"U00004":{}}
tools_dict = {}

def track_progress(user_id, session_id, process):
    global sessions_dict
    try:
        for current_progress in process.stdout:
            current_progress = current_progress.strip()
            if not current_progress.isdigit():
                continue
            if sessions_dict[user_id][session_id]["status"] == "Killed":
                del sessions_dict[user_id][session_id]     # Remove session if killed
                break
            sessions_dict[user_id][session_id]["progress"] = int(current_progress)
        
        process.wait()

        if sessions_dict[user_id][session_id]["status"] != "Killed":
            sessions_dict[user_id][session_id]["progress"] = 100
            sessions_dict[user_id][session_id]["status"] = "Done"
            with open("sessions_data/finished_sessions.txt", "a") as f:
                f.write(f"{user_id}\t{session_id}\t{time.strftime('%H:%M')}\n")

    except KeyError:
        pass  # Session deleted while tracking


@app.route("/")
def home():
    return render_template("main.html")  # Loads templates/index.html

@app.get("/tools-data")
def get_tools_data():
    global tools_dict

    df = pd.read_excel("tools_data.xlsx")

    tools_dict = {}
    for _, row in df.iterrows():
        tool_id = row['ID']
        if tool_id not in tools_dict:
            tools_dict[tool_id] = []
        
        tool_info = {
            'Name': row['Name'],
            'Description': row['Description'],
            'Tool_Name': row['Tool_Name'],
            "Category": row['Category'],
            'Category_ID': row['Category_ID']
        }
        tools_dict[tool_id].append(tool_info)

    return tools_dict

@app.get("/sessions-all/<string:user_id>")
def get_all_sessions(user_id):
    global sessions_dict
    
    user_running_sesseions_dict = {}
    user_finished_sessions = []
    sessions_to_remove = []

    if user_id not in sessions_dict:
        sessions_dict[user_id] = {}

    with open("sessions_data/finished_sessions.txt", "r") as sessions_log_file:
        all_finished_sessions = sessions_log_file.readlines()
        i = 1
        for finished_session in all_finished_sessions:
            if i > 5:
                break
            if user_id in finished_session:
                i += 1
                user_finished_sessions.append([finished_session.split("\t")[1].strip(), finished_session.split("\t")[2].strip()])

    for running_session, session_data in sessions_dict[user_id].items():
        if session_data["status"] in ["Done", "Killed"]:
            sessions_to_remove.append(running_session)
            continue
        
        user_running_sesseions_dict[running_session] = {"progress": session_data["progress"],"status": session_data["status"], "timestamp":session_data["timestamp"]}

    for session in sessions_to_remove:
        del sessions_dict[user_id][session]

    return {"Running":user_running_sesseions_dict, "Done":user_finished_sessions}



@app.route("/session-run/<string:session_id>-<string:user_id>", methods=['POST'])
def run(session_id, user_id):
    global sessions_dict
    global tools_dict


    input_file = request.files['file']
    input_file_path = f"temp_input_files\\input_{session_id}_{user_id}.txt"
    output_file_path = f"output_files\\output_{session_id}_{user_id}.txt"
    input_file.save(input_file_path)

    tool_id = request.form['tool_id']
    mode = request.form['mode']

    tool_path = f'python_tools\\{tools_dict[tool_id][0]["Tool_Name"]}.py'
    
    with open(output_file_path, "w") as output_file:
        pass

    session_id = str(session_id)
    user_id = str(user_id)
    
    process = subprocess.Popen(
        [sys.executable,"-u", tool_path, input_file_path, output_file_path, mode],
        stdout=subprocess.PIPE,   # Capture stdout, stderr to same pipe
        stderr=subprocess.STDOUT,   # Capture stderr
        text=True                  # Decode to string instead of bytes
    )
    

    sessions_dict[user_id][session_id] = {"progress": 0, "process": process, "status":"Running", "timestamp":time.strftime("%H:%M"), "output_path": output_file_path, "tool_id":tool_id}
    
    threading.Thread(target=track_progress, args=(user_id, session_id, process), daemon=True).start()

    return {"message": f"Session {session_id} Running"}




@app.get("/session-refresh/<string:session_id>-<string:user_id>")
def update(session_id, user_id):
    global sessions_dict
    if session_id in sessions_dict[user_id] and sessions_dict[user_id][session_id]["status"] in ["Running", "Killed"]:
        return {"progress": sessions_dict[user_id][session_id]["progress"], "status": sessions_dict[user_id][session_id]["status"]}
    
    return {"progress": 100, "status": "Done"}

@app.get("/session-kill/<string:session_id>-<string:user_id>")

def kill(session_id, user_id):
    if user_id in sessions_dict and session_id in sessions_dict[user_id]:
        proc = sessions_dict[user_id][session_id]["process"]
        proc.terminate()
        sessions_dict[user_id][session_id]["status"] = "Killed"
        return {"message": f"Session {session_id} Killed"}
    return {"message": "Session not found"}

@app.get("/session-download/<string:session_id>-<string:user_id>")
def session_download(session_id, user_id):
    output_path = f"output_files\\output_{session_id}_{user_id}.txt"
    try:
        return send_file(
            output_path,
            as_attachment=True,
            download_name=f"output_{session_id}_{user_id}.txt",
            mimetype="text/plain"
        )
    except FileNotFoundError:
        return {"message": "File not found"}, 410

if __name__ == '__main__':
    app.run(debug=True)