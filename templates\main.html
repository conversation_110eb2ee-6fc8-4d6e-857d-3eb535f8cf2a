<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles-retro.css') }}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" /> -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@24,200,0,0"/>
    <title>AutoSpace</title>
</head>
<body>
    <div class="side-bar-container">   
    <!-- Your HTML (added data-labels for tooltips + ARIA for a11y) -->
    <div class="side-bar" role="tablist" aria-orientation="vertical">
    <span tabindex="0" class="tool-category material-symbols-outlined comparison" id="1" role="tab" data-label="Compare">orbit</span>
    <span tabindex="0" class="tool-category material-symbols-outlined search" id="2" role="tab" data-label="Search">travel_explore</span>
    <span tabindex="0" class="tool-category material-symbols-outlined extraction" id="3" role="tab" data-label="Extract">satellite_alt</span>
    </div>
    </div>
    
    <div class="main-content">
        <div class="tools-cards-section section">
                <h3 class="category-title"></h3>
                <div class="tools-cards-content">

                </div>
        </div>

        <div class = "middle-section section">
            <div class = "autospace-section section">
                <h3 class="box-title">
                    <i class="material-symbols-outlined">rocket_launch</i> AUTOSPACE
                </h3>
                <div class = "default-view">
                    please select a tool to view its information
                </div>

                <div class = "selection-view">
                    <span class = "tool-name"></span>

                    <span class = "tool-description"></span>

                    <span class = "tool-buttons">

                        <button class = 'run-button'>
                            Run
                        </button>

                        <label for="fileInput" class="file-label">📂 Upload File</label>
                        <input type="file" id="fileInput" accept=".txt">

                        <button class = 'download-sample-button'>
                            Download Sample
                        </button>

                        <input type="checkbox" id="mode" name="mode" value="fast">
                        <label for="mode">Fast Mode</label><br>


                    </span>
                </div>

            </div>
                <div class="sessions-section section">
                    <div class= 'sessions-container running-sessions'></div>
                    <div class= 'sessions-container finished-sessions'></div>
                </div>
        </div>

        <!-- section added by AI -->    
        <!-- Right Section -->
        <div class="right-section section">
            <!-- System Performance Section -->
            <div class="system-performance-section">
                <h3 class="box-title">
                    <i class="material-symbols-outlined">monitoring</i> System Performance
                </h3>
                <div class="performance-box box">
                    <div class="performance-metrics grid-2x2">
                        <div class="metric-card cpu-card">
                            <div class="metric-icon">
                                <i class="material-symbols-outlined">memory</i>
                            </div>
                            <div class="metric-content">
                                <span class="metric-label">CPU Cores</span>
                                <span class="metric-value" id="cpu-cores">Loading...</span>
                            </div>
                        </div>

                        <div class="metric-card uptime-card">
                            <div class="metric-icon">
                                <i class="material-symbols-outlined">schedule</i>
                            </div>
                            <div class="metric-content">
                                <span class="metric-label">Uptime</span>
                                <span class="metric-value" id="uptime">Loading...</span>
                            </div>
                        </div>

                        <div class="metric-card memory-card">
                            <div class="metric-icon">
                                <i class="material-symbols-outlined">storage</i>
                            </div>
                            <div class="metric-content">
                                <span class="metric-label">Memory</span>
                                <span class="metric-value" id="memory">Loading...</span>
                            </div>
                        </div>

                        <div class="metric-card platform-card">
                            <div class="metric-icon">
                                <i class="material-symbols-outlined">computer</i>
                            </div>
                            <div class="metric-content">
                                <span class="metric-label">Platform</span>
                                <span class="metric-value" id="platform">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Active Users Section -->
            <div class="active-users-section">
                <h3 class="box-title">
                    <i class="material-symbols-outlined">group</i> Active Users
                    <span class="user-count" id="active-users-count">0</span>
                </h3>
                <div class="users-box box">
                    <div class="users-container" id="active-users-list">
                        <div class="no-users">
                            <i class="material-symbols-outlined">info</i>
                            <p>Loading users...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--end of section added by ai-->
    </div>
    
    <script>

        // directly after opening the page we need to update it with running and done sessions
        async function getAllSessions(){
                // first we need to clear the containers to avoid duplications                
                runningCardsContainer.innerHTML = '<h2>Active Sessions</h2>'
                finishedCardsContainer.innerHTML = '<h2>Finished Sessions</h2>'
                
                // sending request to get all sessions                                
                const response = await fetch(`/sessions-all/${currentUserId}`)
                const data = await response.json();
                const finished_sessions = data.Done
                const running_sessions = data.Running
                
                // creating cards for running sessions                 
                Object.keys(running_sessions).forEach(session_id => {
                    
                    const runCardReload = createSessionCard(session_id, currentUserId, running_sessions[session_id].progress, running_sessions[session_id].timestamp)                    
                    runningCardsContainer.appendChild(runCardReload)
                });
                 
                finished_sessions.forEach((session_data)=> {                
                const doneCardReload = createDoneCard(session_data[0], session_data[1])                
                finishedCardsContainer.appendChild(doneCardReload)
                })
            } 

        
        async function getToolsData(){
            const response = await fetch('/tools-data')
            const data = await response.json();
            toolsData = data
            categoryList.forEach((category)=>{
            category.addEventListener('click', ()=>{
                categoryTitle.innerHTML = `<h2>${category.classList[2].toLocaleUpperCase()}</h2>`
                toolCardsSection.innerHTML = ''
                Object.keys(toolsData).forEach(tool_id => {
                    if (toolsData[tool_id][0].Category_ID.toString() == category.id){
                        toolCard = createToolCard(toolsData[tool_id], tool_id)
                        toolCardsSection.appendChild(toolCard)
                    }
                });
            })
        })
        }
        // function to run the tool and assigning done with request is back        
        async function runTool(session_id){                
                if (inputFile == null){
                    alert("Please upload a file")
                    return
                }

                const formData = new FormData();
                const modeCheckbox = document.getElementById('mode');
                formData.append('mode', modeCheckbox.checked ? 'fast' : 'normal');
                formData.append('file', inputFile);
                formData.append('tool_id', currentToolID);
                // sending request to run the tool and get the progress back                
                const response = await fetch(`/session-run/${session_id}-${currentUserId}`
                ,{method: 'POST', body: formData})

                const data = await response.json();
                inputFile = null;
                fileInput.value = "";
                return true
            }

        function createToolCard(toolCardData, toolId){
            const toolCard = document.createElement('div')
            toolCard.className = `tool-card`
            toolCard.id = toolId
            toolCard.classList.add(toolCardData[0].Tool_Name)
            toolCard.innerHTML = `<h3>${toolCardData[0].Name}</h3>`
            toolCard.addEventListener('click', ()=>{
                toolDescrtiption.innerHTML = toolCardData[0].Description
                toolName.innerHTML = toolCardData[0].Name
                spaceDefaultView.style.display = "none"
                spaceSelectionView.style.display = "flex"

                currentToolID = toolId
            })
            
            return toolCard
        }

        function createSessionCard(currentSessionId, currentUserId, progress, currentTime){            
            const currentRunCard = document.createElement('div')
            currentRunCard.className = `session-${currentSessionId} session-card`
            currentRunCard.id = currentSessionId
            currentRunCard.innerHTML = `<div class="title">Session #${currentSessionId}</div>`
            
            const refresh_button = create_refresh_button(currentSessionId)
            const kill_button = create_kill_button(currentSessionId)
            const progressCounter = create_progress_counter(progress)            

            const timeCard = create_time_label(currentTime)

            currentRunCard.appendChild(timeCard)            
            currentRunCard.appendChild(refresh_button)
            currentRunCard.appendChild(kill_button)
            currentRunCard.appendChild(progressCounter)
            
            refresh_button.addEventListener('click', async () => {
                const response = await fetch(`/session-refresh/${currentSessionId}-${currentUserId}`)
                const data = await response.json()
                progressCounter.textContent = `${data.progress}%`
                if (data.status == "Done"){
                    move_card_to_done(currentRunCard, data.status)                    
                }
            })

            kill_button.addEventListener('click', async () => {
                const response = await fetch(`/session-kill/${currentSessionId}-${currentUserId}`)
                const data = await response.json()
                move_card_to_done(currentRunCard, "Killed")
            })
                        
            return currentRunCard
        }


        function createDoneCard(currentSessionId, currentTime){
            const currentDoneCard = document.createElement('div')
            currentDoneCard.className = `done-session-${currentSessionId} session-card`
            currentDoneCard.innerHTML = `<div class="title">Session #${currentSessionId}</div>`
            const time = create_time_label(currentTime)
            currentDoneCard.appendChild(time)
            const download_button = create_download_button(currentSessionId)
            currentDoneCard.appendChild(download_button)
            return currentDoneCard
        }

        function create_refresh_button(session_id){
            // creating refresh button that tracks the session progress
            const currentRefreshButton = document.createElement('button')
            currentRefreshButton.className = `refresh-button`
            currentRefreshButton.dataset.sessionId = `${session_id}`
            currentRefreshButton.innerHTML = "REFRESH"
            return currentRefreshButton
        }

        function create_kill_button(session_id){
            // creating kill button that kills the session
            const currentKillButton = document.createElement('button')
            currentKillButton.className = `kill-button`
            currentKillButton.dataset.sessionId = `${session_id}`
            currentKillButton.innerHTML = "KILL"
            return currentKillButton
        }

        function create_progress_counter(progress){            
            const progressCounter = document.createElement('span')
            progressCounter.className = `progress-counter`
            progressCounter.innerHTML = `${progress}%`            
            return progressCounter
        }


        function move_card_to_done(currentRunCard, status){
            
            if (status == "Killed"){
                if (currentRunCard.parentElement === runningCardsContainer){
                    runningCardsContainer.removeChild(currentRunCard)
                    return
                }
                
            }

            const refresh_button = currentRunCard.querySelector('.refresh-button')
            const kill_button = currentRunCard.querySelector('.kill-button')
            const progressCounter = currentRunCard.querySelector('.progress-counter')
            
            const time = create_time_label()
            currentRunCard.appendChild(time)            

            progressCounter.textContent = `${status}`            
            currentRunCard.classList.add('done-session')
            

            const download_button = create_download_button(currentRunCard.id)
            currentRunCard.appendChild(download_button)

            currentRunCard.removeChild(refresh_button)
            currentRunCard.removeChild(kill_button)

            finishedCardsContainer.appendChild(currentRunCard)            
        }

        function create_download_button(session_id){
            const download_button = document.createElement('button');
            download_button.className = `download-button`;
            download_button.innerHTML = "DOWNLOAD";

            download_button.addEventListener('click', async ()=>{
                const response = await fetch(`/session-download/${session_id}-${currentUserId}`);
                if (!response.ok) { alert("Download failed."); return; }
                const blob = await response.blob();
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `output_${session_id}_${currentUserId}.txt`; // nice filename
                document.body.appendChild(a);
                a.click();
                a.remove();
                URL.revokeObjectURL(url);
            });
            return download_button;
        }


        function create_time_label(currentTime){

            const time = document.createElement('span')

            time.className = `time`
            if (currentTime == null || currentTime == undefined){
                time.innerHTML = getCurrentTimeHHMM()                
            }
            else{
                time.innerHTML = currentTime                
            }
            return time
        }

        function getCurrentTimeHHMM() {
            const now = new Date();
            const hours = String(now.getHours()).padStart(2, "0");   // ensure 2 digits
            const minutes = String(now.getMinutes()).padStart(2, "0"); // ensure 2 digits
            return `${hours}:${minutes}`;
        }

        function makeSessionId() {
            // Example: "S812345-1427" (random + HHMM)
            const rand = Math.floor(100000 + Math.random() * 900000);
            const now = getCurrentTimeHHMM();          // "14:27"
            const hhmm = now.replace(/:/g, "");        // "1427"
            return `S${rand}-${hhmm}`;
        }

        // declaring the elements we will use
        let currentUserId = "U00001" 
        const runButton = document.querySelector(".run-button")
        const runningCardsContainer= document.querySelector(".running-sessions")    
        const finishedCardsContainer = document.querySelector(".finished-sessions")
        // const userList = document.querySelectorAll(".user-selection li")
        const categoryList = document.querySelectorAll(".tool-category")
        const fileInput = document.getElementById('fileInput');
        let inputFile = null
        const toolCardsSection = document.querySelector(".tools-cards-content")
        const categoryTitle = document.querySelector(".category-title")
        let currentToolID = null
        let currentToolDescription = null
        const spaceDefaultView = document.querySelector(".default-view")
        const spaceSelectionView = document.querySelector(".selection-view")
        const toolDescrtiption = document.querySelector(".tool-description")
        const toolName = document.querySelector(".tool-name")
        
        spaceSelectionView.style.display = "none"
        getToolsData()
        // load all sessions [finished and running] to see realtime update
        getAllSessions()
        //section added by AI
        // Initialize system performance monitoring
        initializeSystemPerformance()
        // Initialize active users monitoring
        initializeActiveUsers()
        // End of Section Added by AI

        fileInput.addEventListener('change', (event)=> {
            inputFile = event.target.files[0]
        });
       

        // adding the functionality for the runButton
        runButton.addEventListener('click', async ()=>{
            // creating a random number for session id
            const currentTime = getCurrentTimeHHMM()
            session_id = makeSessionId()
            // before creating the card we send request to run tools and make sure we get the response to create the card
            const session_status  = await runTool(session_id)

            if (session_status){
            // creating the session card and assigning the session id
            const runCard = createSessionCard(session_id, currentUserId, 0, currentTime)            
            
            runningCardsContainer.appendChild(runCard)}

        })

        // Section Added by AI
        // System Performance Monitoring
        function initializeSystemPerformance() {
            // Get CPU cores
            const cpuCores = navigator.hardwareConcurrency || 'Unknown';
            document.getElementById('cpu-cores').textContent = cpuCores;

            // Get memory (if available)
            const memory = navigator.deviceMemory ? `${navigator.deviceMemory} GB` : 'Unknown';
            document.getElementById('memory').textContent = memory;

            // Get platform
            const platform = navigator.platform || 'Unknown';
            document.getElementById('platform').textContent = platform;

            // Initialize uptime counter
            const startTime = Date.now();
            updateUptime(startTime);

            // Update uptime every second
            setInterval(() => updateUptime(startTime), 1000);
        }

        function updateUptime(startTime) {
            const uptime = Math.floor((Date.now() - startTime) / 1000);
            const hours = Math.floor(uptime / 3600);
            const minutes = Math.floor((uptime % 3600) / 60);
            const seconds = uptime % 60;

            let uptimeText = '';
            if (hours > 0) {
                uptimeText = `${hours}h ${minutes}m ${seconds}s`;
            } else if (minutes > 0) {
                uptimeText = `${minutes}m ${seconds}s`;
            } else {
                uptimeText = `${seconds}s`;
            }

            document.getElementById('uptime').textContent = uptimeText;
        }

        // Active Users Monitoring
        function initializeActiveUsers() {
            // Simulate active users data
            loadActiveUsers();

            // Update active users every 30 seconds
            setInterval(loadActiveUsers, 30000);
        }

        function loadActiveUsers() {
            // Simulate active users data (replace with actual API call)
            const mockUsers = [
                {
                    username: 'Current User',
                    isCurrentUser: true,
                    lastActivity: 'Just now',
                    activityType: 'Active',
                    sessionCount: 2
                },
                {
                    username: 'User_001',
                    isCurrentUser: false,
                    lastActivity: '2 min ago',
                    activityType: 'Processing',
                    sessionCount: 1
                },
                {
                    username: 'User_002',
                    isCurrentUser: false,
                    lastActivity: '5 min ago',
                    activityType: 'Idle',
                    sessionCount: 0
                }
            ];

            displayActiveUsers(mockUsers);
        }

        function displayActiveUsers(users) {
            const activeUsersContainer = document.getElementById('active-users-list');
            const activeUsersCount = document.getElementById('active-users-count');

            if (users.length === 0) {
                activeUsersContainer.innerHTML = `
                    <div class="no-users">
                        <i class="material-symbols-outlined">info</i>
                        <p>No active users</p>
                    </div>`;
                activeUsersCount.textContent = '0';
                return;
            }

            const userCards = users.map(user => createUserCard(user)).join('');
            activeUsersContainer.innerHTML = userCards;
            activeUsersCount.textContent = users.length;
        }

        function createUserCard(user) {
            const userIcon = user.isCurrentUser ?
                '<i class="material-symbols-outlined">account_circle</i>' :
                '<i class="material-symbols-outlined">person</i>';

            const sessionBadge = user.sessionCount > 0 ?
                `<span class="session-badge">${user.sessionCount} sessions</span>` : '';

            const currentUserClass = user.isCurrentUser ? 'current-user' : '';

            return `
                <div class="user-card ${currentUserClass}">
                    <div class="user-header">
                        ${userIcon}
                        <span class="username">${user.username}${user.isCurrentUser ? ' (You)' : ''}</span>
                        ${sessionBadge}
                    </div>
                    <div class="user-info">
                        <span class="last-activity">${user.lastActivity}</span>
                        <span class="activity-type">${user.activityType}</span>
                    </div>
                </div>`;
        }
        // End of Section Added by AI
    </script>
</body>
</html>