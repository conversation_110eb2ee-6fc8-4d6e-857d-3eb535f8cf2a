/* Modern Gradient Theme - Contemporary design with vibrant gradients and smooth animations
 * Design Concept: Modern UI with colorful gradients, smooth transitions, and contemporary aesthetics
 * Key Features: Vibrant color gradients, smooth animations, modern typography, dynamic backgrounds
 */

:root {
  /* Gradient Colors */
  --glass-bg-primary: rgba(255, 255, 255, 0.15);
  --glass-bg-secondary: rgba(255, 255, 255, 0.1);
  --glass-bg-tertiary: rgba(255, 255, 255, 0.2);
  --glass-bg-hover: rgba(255, 255, 255, 0.25);

  /* Gradient Accents */
  --accent-black: #1a1a2e;
  --accent-blue-primary: #6366f1;
  --accent-blue-secondary: #8b5cf6;
  --accent-blue-light: #a78bfa;
  --accent-blue-dark: #4f46e5;

  /* Modern Text Colors */
  --text-white-primary: rgba(255, 255, 255, 0.95);
  --text-white-secondary: rgba(255, 255, 255, 0.85);
  --text-white-tertiary: rgba(255, 255, 255, 0.7);
  --text-white-muted: rgba(255, 255, 255, 0.5);

  /* Modern Effects */
  --glass-blur: blur(20px);
  --glass-blur-light: blur(12px);
  --glass-border: 1px solid rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  --glass-shadow-hover: 0 12px 40px rgba(0, 0, 0, 0.4);

  /* Transitions */
  --transition-smooth: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  --transition-fast: all 0.2s ease;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', 'SF Pro Display', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
  background-attachment: fixed;
  margin: 0;
  display: flex;
  flex-direction: row;
  min-height: 100vh;
  color: var(--text-white-primary);
  overflow-x: hidden;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.side-bar-container {
  display: flex;
  height: 100vh;
  width: 100px;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
}

.side-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 40px;
  padding: 25px;
  width: 60px;
  height: 50vh;
  border-radius: 25px;
  background: var(--glass-bg-primary);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  box-shadow: var(--glass-shadow);
  transition: var(--transition-smooth);
  border: var(--glass-border);
  position: relative;
  overflow: hidden;
}

.side-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1), rgba(167, 139, 250, 0.1));
  opacity: 0;
  transition: var(--transition-smooth);
  z-index: -1;
  border-radius: 25px;
}

.side-bar:hover {
  background: var(--glass-bg-hover);
  box-shadow: var(--glass-shadow-hover);
  transform: translateY(-3px) scale(1.02);
}

.side-bar:hover::before {
  opacity: 1;
}

.tool-category {
  color: var(--text-white-primary);
  display: flex;
  padding: 12px;
  border-radius: 50%;
  cursor: pointer;
  transition: var(--transition-smooth);
  background: var(--glass-bg-secondary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  position: relative;
  overflow: hidden;
}

.tool-category::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, var(--accent-blue-primary), var(--accent-blue-secondary), var(--accent-blue-light));
  opacity: 0;
  transition: var(--transition-smooth);
  z-index: -1;
  border-radius: 50%;
}

.tool-category:hover {
  background: var(--glass-bg-tertiary);
  color: var(--text-white-primary);
  transform: scale(1.15) rotate(5deg);
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
}

.tool-category:hover::before {
  opacity: 0.3;
}

.tool-category[aria-selected="true"] {
  background: linear-gradient(45deg, var(--accent-blue-primary), var(--accent-blue-secondary));
  color: var(--text-white-primary);
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.6);
}

.main-content {
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  padding: 20px;
  gap: 20px;
}

.tools-cards-section {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 10px;
  border-radius: 25px;
  overflow-y: auto;
  max-width: 270px;
}

.tools-cards-section h2 {
  align-self: flex-start;
  margin-bottom: 10px;
}

.tools-cards-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.middle-section {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 30px;
  overflow-y: auto;
}

.sessions-section {
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  padding: 30px;
  overflow-y: auto;
  gap: 20px;
}

.finished-sessions, .running-sessions {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 25px;
  background: var(--glass-bg-secondary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 20px;
  box-shadow: var(--glass-shadow);
  overflow-y: auto;
  transition: var(--transition-smooth);
  border: var(--glass-border);
  position: relative;
}

.finished-sessions::before, .running-sessions::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
  opacity: 0;
  transition: var(--transition-smooth);
  z-index: -1;
  border-radius: 20px;
}

.finished-sessions:hover, .running-sessions:hover {
  background: var(--glass-bg-tertiary);
  transform: translateY(-3px) scale(1.01);
  box-shadow: var(--glass-shadow-hover);
}

.finished-sessions:hover::before, .running-sessions:hover::before {
  opacity: 1;
}

/* Typography Styles */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-white-primary);
  font-weight: 700;
  margin-bottom: 20px;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

h1 {
  font-size: 2.5rem;
  background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientText 3s ease-in-out infinite;
}

@keyframes gradientText {
  0%, 100% { filter: hue-rotate(0deg); }
  50% { filter: hue-rotate(90deg); }
}

h2 {
  font-size: 2rem;
  color: var(--text-white-primary);
}

h3 {
  font-size: 1.5rem;
  color: var(--text-white-secondary);
}

/* AutoSpace Section */
.autospace-section {
  border-radius: 20px;
  padding: 25px;
  margin-bottom: 20px;
}

.default-view {
  color: var(--text-white-tertiary);
  font-size: 1.1rem;
  text-align: center;
  padding: 40px 20px;
  background: var(--glass-bg-secondary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 15px;
  font-style: italic;
  border: var(--glass-border);
}

.selection-view {
  display: flex;
  flex-direction: column;
  gap: 20px;
  background: var(--glass-bg-secondary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 15px;
  padding: 25px;
  border: var(--glass-border);
  position: relative;
  overflow: hidden;
}

.selection-view::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05), rgba(139, 92, 246, 0.05));
  z-index: -1;
  border-radius: 15px;
}

.tool-name {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-white-primary);
  margin-bottom: 10px;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  background: linear-gradient(45deg, var(--accent-blue-primary), var(--accent-blue-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.tool-description {
  font-size: 1.1rem;
  color: var(--text-white-secondary);
  line-height: 1.6;
  margin-bottom: 20px;
}

.tool-buttons {
  display: flex;
  flex-direction: row;
  gap: 15px;
  align-items: flex-start;
}

/* Button Styles */
.run-button, .download-sample-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 15px 30px;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-white-primary);
  background: linear-gradient(45deg, var(--accent-blue-primary), var(--accent-blue-secondary));
  border: none;
  border-radius: 50px;
  cursor: pointer;
  transition: var(--transition-smooth);
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: var(--glass-shadow);
  position: relative;
  overflow: hidden;
}

.run-button::before, .download-sample-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, var(--accent-blue-secondary), var(--accent-blue-light));
  opacity: 0;
  transition: var(--transition-smooth);
  z-index: -1;
  border-radius: 50px;
}

.run-button:hover, .download-sample-button:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 15px 35px rgba(99, 102, 241, 0.4);
  color: var(--text-white-primary);
}

.run-button:hover::before, .download-sample-button:hover::before {
  opacity: 1;
}

.run-button:active, .download-sample-button:active {
  transform: translateY(-1px) scale(1.02);
}

/* File Input Styles */
.file-label {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 15px 30px;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-white-primary);
  background: linear-gradient(45deg, var(--accent-blue-dark), var(--accent-blue-primary));
  border-radius: 50px;
  cursor: pointer;
  transition: var(--transition-smooth);
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: var(--glass-shadow);
  position: relative;
  overflow: hidden;
}

.file-label::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, var(--accent-blue-primary), var(--accent-blue-light));
  opacity: 0;
  transition: var(--transition-smooth);
  z-index: -1;
  border-radius: 50px;
}

.file-label:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 15px 35px rgba(99, 102, 241, 0.4);
}

.file-label:hover::before {
  opacity: 1;
}

#fileInput {
  display: none;
}

/* Checkbox and Label Styles */
input[type="checkbox"] {
  appearance: none;
  width: 20px;
  height: 20px;
  background: var(--glass-bg-secondary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  transition: var(--transition-smooth);
  margin-right: 10px;
  border: 2px solid transparent;
  background-clip: padding-box;
}

input[type="checkbox"]::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, var(--accent-blue-primary), var(--accent-blue-secondary));
  border-radius: 6px;
  z-index: -1;
}

input[type="checkbox"]:checked {
  background: linear-gradient(45deg, var(--accent-blue-primary), var(--accent-blue-secondary));
  box-shadow: 0 0 15px rgba(99, 102, 241, 0.5);
}

input[type="checkbox"]:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--text-white-primary);
  font-weight: bold;
  font-size: 14px;
}

label[for="mode"] {
  color: var(--text-white-secondary);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-fast);
}

label[for="mode"]:hover {
  color: var(--text-white-primary);
}

/* Right Section Styles */
.right-section {
  display: flex;
  flex-direction: column;
  width: 350px;
  gap: 20px;
  flex-grow: .5;
}

.system-performance-section, .active-users-section {
  padding: 25px;
}

.box-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-white-primary);
  margin-bottom: 20px;
  text-transform: uppercase;
  letter-spacing: 1px;
  background: linear-gradient(45deg, var(--accent-blue-primary), var(--accent-blue-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.box-title i {
  color: var(--accent-blue-light);
  font-size: 1.5rem;
  background: linear-gradient(45deg, var(--accent-blue-primary), var(--accent-blue-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.performance-box, .users-box {
  border-radius: 15px;
  padding: 20px;
}

.performance-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.metric-card {
  background: var(--glass-bg-tertiary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 15px;
  padding: 15px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: var(--transition-smooth);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
  opacity: 0;
  transition: var(--transition-smooth);
  z-index: -1;
  border-radius: 15px;
}

.metric-card:hover {
  background: var(--glass-bg-hover);
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
}

.metric-card:hover::before {
  opacity: 1;
}

.metric-icon i {
  color: var(--accent-blue-light);
  font-size: 1.8rem;
  background: linear-gradient(45deg, var(--accent-blue-primary), var(--accent-blue-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.metric-content {
  display: flex;
  flex-direction: column;
}

.metric-label {
  font-size: 0.85rem;
  color: var(--text-white-tertiary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metric-value {
  font-size: 1.1rem;
  color: var(--text-white-primary);
  font-weight: 600;
}

/* User Count Badge */
.user-count {
  background: linear-gradient(45deg, var(--accent-blue-primary), var(--accent-blue-secondary));
  color: var(--text-white-primary);
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  margin-left: auto;
  box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
}

/* Users Container */
.users-container {
  max-height: 300px;
  overflow-y: auto;
}

.no-users {
  text-align: center;
  padding: 30px 20px;
  color: var(--text-white-tertiary);
}

.no-users i {
  font-size: 2rem;
  margin-bottom: 10px;
  color: var(--accent-blue-light);
  background: linear-gradient(45deg, var(--accent-blue-primary), var(--accent-blue-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.user-card {
  background: var(--glass-bg-tertiary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 15px;
  padding: 15px;
  margin-bottom: 10px;
  transition: var(--transition-smooth);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.user-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
  opacity: 0;
  transition: var(--transition-smooth);
  z-index: -1;
  border-radius: 15px;
}

.user-card:hover {
  background: var(--glass-bg-hover);
  transform: translateX(5px) scale(1.02);
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
}

.user-card:hover::before {
  opacity: 1;
}

.user-card.current-user {
  background: linear-gradient(135deg, var(--glass-bg-tertiary), rgba(99, 102, 241, 0.3));
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
}

.user-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.user-header i {
  color: var(--accent-blue-light);
  font-size: 1.2rem;
  background: linear-gradient(45deg, var(--accent-blue-primary), var(--accent-blue-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.username {
  color: var(--text-white-primary);
  font-weight: 600;
  flex-grow: 1;
}

.session-badge {
  background: linear-gradient(45deg, var(--accent-blue-primary), var(--accent-blue-secondary));
  color: var(--text-white-primary);
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 0.75rem;
  font-weight: 500;
}

.user-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.85rem;
}

.last-activity {
  color: var(--text-white-tertiary);
  font-style: italic;
}

.activity-type {
  color: var(--text-white-secondary);
  background: var(--glass-bg-secondary);
  padding: 2px 8px;
  border-radius: 8px;
  font-weight: 500;
}

/* Tool Cards Styles */
.tool-card {
  background: var(--glass-bg-secondary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 20px;
  padding: 20px;
  margin-bottom: 15px;
  cursor: pointer;
  transition: var(--transition-smooth);
  box-shadow: var(--glass-shadow);
  position: relative;
  overflow: hidden;
  border: var(--glass-border);
}

.tool-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
  opacity: 0;
  transition: var(--transition-smooth);
  z-index: -1;
  border-radius: 20px;
}

.tool-card:hover {
  background: var(--glass-bg-tertiary);
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 15px 40px rgba(99, 102, 241, 0.3);
}

.tool-card:hover::before {
  opacity: 1;
}

.tool-card h3 {
  color: var(--text-white-primary);
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 10px;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  background: linear-gradient(45deg, var(--accent-blue-primary), var(--accent-blue-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.tool-card p {
  color: var(--text-white-secondary);
  font-size: 0.95rem;
  line-height: 1.5;
}

/* Session Cards Styles */
.session-card {
  background: var(--glass-bg-secondary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 15px;
  padding: 18px;
  margin-bottom: 12px;
  transition: var(--transition-smooth);
  box-shadow: var(--glass-shadow);
  position: relative;
  overflow: hidden;
  border: var(--glass-border);
}

.session-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, var(--accent-blue-primary), var(--accent-blue-secondary));
  transition: var(--transition-smooth);
  border-radius: 0 0 0 15px;
}

.session-card:hover {
  background: var(--glass-bg-tertiary);
  transform: translateX(5px) scale(1.01);
  box-shadow: 0 8px 30px rgba(99, 102, 241, 0.3);
}

.session-card:hover::before {
  width: 6px;
  background: linear-gradient(to bottom, var(--accent-blue-secondary), var(--accent-blue-light));
}

.session-card .title {
  color: var(--text-white-primary);
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 10px;
}

.session-card .time {
  color: var(--text-white-tertiary);
  font-size: 0.85rem;
  background: var(--glass-bg-primary);
  padding: 4px 10px;
  border-radius: 15px;
  display: inline-block;
  margin-bottom: 10px;
}

.progress-counter {
  color: var(--accent-blue-light);
  font-weight: 600;
  font-size: 0.9rem;
  background: linear-gradient(45deg, rgba(99, 102, 241, 0.2), rgba(139, 92, 246, 0.2));
  padding: 4px 10px;
  border-radius: 12px;
  display: inline-block;
  margin-bottom: 10px;
}

/* Session Action Buttons */
.refresh-button, .kill-button, .download-button {
  background: linear-gradient(45deg, var(--glass-bg-primary), var(--glass-bg-secondary));
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  color: var(--text-white-primary);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-smooth);
  margin-right: 8px;
  margin-top: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.refresh-button:hover {
  background: linear-gradient(45deg, var(--accent-blue-primary), var(--accent-blue-secondary));
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(99, 102, 241, 0.4);
}

.kill-button:hover {
  background: linear-gradient(45deg, #ef4444, #dc2626);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(239, 68, 68, 0.4);
}

.download-button:hover {
  background: linear-gradient(45deg, #10b981, #059669);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(16, 185, 129, 0.4);
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--glass-bg-secondary);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, var(--accent-blue-primary), var(--accent-blue-secondary));
  border-radius: 10px;
  transition: var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, var(--accent-blue-secondary), var(--accent-blue-light));
}

/* Responsive Design */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
  }

  .right-section {
    width: 100%;
    flex-direction: row;
  }

  .system-performance-section, .active-users-section {
    flex: 1;
  }
}

@media (max-width: 768px) {
  .side-bar-container {
    width: 80px;
  }

  .main-content {
    padding: 15px;
  }

  .right-section {
    flex-direction: column;
  }

  .performance-metrics {
    grid-template-columns: 1fr;
  }
}
